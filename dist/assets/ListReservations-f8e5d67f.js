import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as Se,r as u}from"./vendor-851db8c1.js";import{M as ye,T as ve,G as be,A as je,c as ke,i as O,D as _e,U as Ce,V as Ne,t as De,W as Pe,X as z,v as Te,Y as S,Z as Re}from"./index-ca7cbd3e.js";import{c as Ee,a as m}from"./yup-54691517.js";import{u as Ae}from"./react-hook-form-687afde5.js";import{o as Le}from"./yup-2824f222.js";import{P as Fe}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import $e from"./Skeleton-1e8bf077.js";import{A as Me,C as Oe}from"./CheckinModal-a9c18ddc.js";import{L as ze}from"./LoadingOverlay-87926629.js";import{P as Ve,R as Be,T as Ge,F as Ie}from"./ReservationStatus-0d38d99f.js";import{D as He}from"./DataTable-a2248415.js";import{H as qe}from"./HistoryComponent-cf25af7a.js";let V=new ye,Ke=new ve;const Ue=[{header:"Date",accessor:"date",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"By",accessor:"user",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Sport",accessor:"sport",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Type",accessor:"type",isSorted:!1,isSortedDesc:!1,mappingExist:!1},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{1:"Paid",0:"Reserved",2:"Failed"}},{header:"Action",accessor:""}],We=({sports:d,club:l,courts:y})=>{const{dispatch:v,state:Je}=a.useContext(be),{dispatch:B}=a.useContext(je),[G,I]=a.useState([]),[c,b]=a.useState(10),[H,q]=a.useState(0),[Xe,K]=a.useState(0),[p,U]=a.useState(1),[W,J]=a.useState(!1),[X,Y]=a.useState(!1),[Ye,Z]=a.useState(!1);a.useState(!1),a.useState([]),a.useState([]),a.useState("eq");const[g,f]=a.useState(!0),[Q,Ze]=a.useState(!1),[w,Qe]=a.useState(!1);a.useState(),Se();const j=a.useRef(null),[ee,k]=a.useState(!1),[x,h]=a.useState(null),[se,_]=u.useState(!1),[te,ae]=u.useState(!1),[ie,ne]=u.useState(!1),[C,oe]=u.useState([]),re=Ee({id:m(),email:m(),role:m(),status:m()}),{register:le,handleSubmit:ce,formState:{errors:we}}=Ae({resolver:Le(re)});function de(){n(p-1,c)}function pe(){n(p+1,c)}const ue=async()=>{try{V.setTable("user");const e=await V.callRestAPI({filter:[`club_id,eq,${l==null?void 0:l.id}`,"role,cs,user"]},"GETALL");oe(e.list||[])}catch(e){console.error("Error fetching players:",e),showToast(v,"Error fetching players",3e3,"error")}};async function n(e,t,o={},r=[]){f(!(w||Q));try{const i=await Ke.getPaginate("reservation",{page:e,limit:t,filter:[...r,`courtmatchup_reservation.club_id,cs,${l==null?void 0:l.id}`],join:["clubs|club_id","booking|booking_id","user|user_id"],size:c});i&&(f(!1),I(i.list),b(i.limit),q(i.num_pages),U(i.page),K(i.total),J(i.page>1),Y(i.page+1<=i.num_pages))}catch(i){f(!1),console.log("ERROR",i),De(B,i.message)}}const me=e=>{e.search?n(1,c,{},[`first_name,cs,${e.search}`,`last_name,cs,${e.search}`]):n(1,c)},ge=async e=>{const t=e.target.value;t===""?await n(1,c):await n(1,c,{},[`sport_id,eq,${parseInt(t)}`])},fe=async e=>{e.target.value===""?await n(p,c):await n(p,c,{},[`courtmatchup_booking.reservation_type,cs,${e.target.value}`])},xe=async e=>{e.target.value===""?await n(p,c):await n(p,c,{},[`courtmatchup_booking.status,cs,${e.target.value}`])};a.useEffect(()=>{v({type:"SETPATH",payload:{path:"reservations"}}),l!=null&&l.id&&(n(1,c,{}),ue())},[l==null?void 0:l.id]);const N=e=>{j.current&&!j.current.contains(e.target)&&Z(!1)};a.useEffect(()=>(document.addEventListener("mousedown",N),()=>{document.removeEventListener("mousedown",N)}),[]);const D=e=>{var o,r,i,P,T,R,E,A,L,F,$,M;const t={...e,id:(o=e.booking)==null?void 0:o.id,date:(r=e.booking)==null?void 0:r.date,startTime:(i=e.booking)==null?void 0:i.start_time,endTime:(P=e.booking)==null?void 0:P.end_time,sport_id:(T=e.booking)==null?void 0:T.sport_id,type:(R=e.booking)==null?void 0:R.type,sub_type:(E=e.booking)==null?void 0:E.subtype,reservation_type:(A=e.booking)==null?void 0:A.reservation_type,price:(L=e.booking)==null?void 0:L.price,status:(F=e.booking)==null?void 0:F.status,player_ids:($=e.booking)==null?void 0:$.player_ids,coach_ids:(M=e.booking)==null?void 0:M.coach_ids};h(t),k(!0)},he={"":e=>s.jsx("div",{className:"flex items-center gap-3",children:s.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",onClick:t=>{t.stopPropagation(),D(e)},children:s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})}),type:e=>{var t;return s.jsx("span",{className:"capitalize",children:((t=O.find(o=>{var r;return o.value==((r=e==null?void 0:e.booking)==null?void 0:r.reservation_type)}))==null?void 0:t.label)||"--"})},sport:e=>{var t;return s.jsx("span",{className:"capitalize",children:((t=d.find(o=>{var r;return o.id===((r=e==null?void 0:e.booking)==null?void 0:r.sport_id)}))==null?void 0:t.name)||"--"})},date:e=>{var t,o,r;return s.jsxs(s.Fragment,{children:[Pe((t=e==null?void 0:e.booking)==null?void 0:t.date)," "," | "," ",z((o=e==null?void 0:e.booking)==null?void 0:o.start_time)," "," - "," ",z((r=e==null?void 0:e.booking)==null?void 0:r.end_time)]})},players:e=>{var t,o;return s.jsx(s.Fragment,{children:(t=e==null?void 0:e.booking)!=null&&t.player_ids?`${JSON.parse((o=e==null?void 0:e.booking)==null?void 0:o.player_ids).length} players`:"0 players"})},bill:e=>{var t;return s.jsx(s.Fragment,{children:Te((t=e==null?void 0:e.booking)==null?void 0:t.price)})},user:e=>{var t,o,r,i;return s.jsx(s.Fragment,{children:!((t=e==null?void 0:e.user)!=null&&t.first_name)||!((o=e==null?void 0:e.user)!=null&&o.last_name)?"--":`${(r=e==null?void 0:e.user)==null?void 0:r.first_name} ${(i=e==null?void 0:e.user)==null?void 0:i.last_name}`})},status:e=>s.jsxs(s.Fragment,{children:[e.booking.status==S.SUCCESS&&s.jsx(Ve,{}),e.booking.status==S.PENDING&&s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(Be,{}),s.jsx(Ge,{timeLeft:Re(e==null?void 0:e.reservation_updated_at)})]}),e.booking.status==S.FAIL&&s.jsx(Ie,{})]})};return s.jsxs("div",{className:"h-screen px-8",children:[g&&s.jsx(ze,{}),s.jsxs("div",{className:"flex flex-col gap-6 py-3",children:[s.jsx("div",{className:"w-full max-w-xl",children:s.jsxs("form",{className:"relative flex items-center",onSubmit:ce(me),children:[s.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:s.jsx(ke,{className:"text-gray-500"})}),s.jsx("input",{type:"text",className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search reservations...",...le("search")})]})}),s.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[s.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[s.jsx("input",{type:"date",className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500"}),s.jsx("input",{type:"time",defaultValue:"00:00",className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500"})]}),s.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[s.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700",defaultValue:"Sport: All",onChange:ge,children:[s.jsx("option",{value:"",children:"Sport: All"}),d==null?void 0:d.map(e=>s.jsx("option",{value:e.id,children:e.name},e.id))]}),s.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700",defaultValue:"All",onChange:xe,children:[s.jsx("option",{value:"",children:"Status: All"}),s.jsx("option",{value:"0",children:"Reserved"}),s.jsx("option",{value:"1",children:"Paid"}),s.jsx("option",{value:"2",children:"Failed"})]}),s.jsxs("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700",defaultValue:"All",onChange:fe,children:[s.jsx("option",{value:"",children:"Reservation Type: All"}),O.map(e=>s.jsx("option",{value:e.value,children:e.label},e.value))]})]}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsxs("button",{onClick:()=>_(!0),className:"inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[s.jsx("span",{children:"+"}),"Add new"]}),s.jsx(qe,{activityType:_e.court_reservation,emptyMessage:"No reservations found",title:"Reservations"})]})]})]}),g?s.jsx($e,{}):s.jsx("div",{className:"overflow-x-auto",children:s.jsx(He,{columns:Ue,data:G,loading:g,renderCustomCell:he,rowClassName:"hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer",cellClassName:"whitespace-nowrap px-6 py-4",headerClassName:"px-6 py-4 text-left text-sm font-medium text-gray-500",emptyMessage:"No data available",onClick:e=>D(e)})}),s.jsx(Fe,{currentPage:p,pageCount:H,pageSize:c,canPreviousPage:W,canNextPage:X,updatePageSize:e=>{b(e),n(1,e)},previousPage:de,nextPage:pe,gotoPage:e=>n(e,c)}),s.jsx(Ce,{isOpen:ee,onClose:()=>k(!1),event:x,users:C,sports:d,club:l,fetchData:n,courts:y}),s.jsx(Me,{isOpen:se,club:l,onClose:()=>_(!1),sports:d,players:C}),te&&s.jsx(Oe,{courts:y,onClose:()=>ae(!1),reservation:x,getData:n,sports:d,setReservation:h}),ie&&s.jsx(Ne,{reservation:x,onClose:()=>ne(!1),getData:n,setReservation:h})]})},gs=We;export{gs as L};
