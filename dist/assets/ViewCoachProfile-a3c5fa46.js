import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{j as Ce,r as n,b as te}from"./vendor-851db8c1.js";import{G as se,e as Ee,M as Pe,T as Te,d as ae,b as j,E as P,D as T,H as A,a2 as O,v as Ae}from"./index-ca7cbd3e.js";import"./BottomDrawer-f2e7890f.js";import{h as D}from"./moment-a9aaa855.js";import{I as Ie}from"./ImageCropModal-d613e0c3.js";import{P as H}from"./PencilIcon-35185602.js";import{P as $e}from"./PlusIcon-7e8d14d7.js";import{T as Fe}from"./TrashIcon-7d213648.js";let c=new Pe,ee=new Te;const ke=({isOpen:r,onClose:N,onSave:h,sportData:t,clubSports:f,isEditing:u})=>{var M,z;const[m,w]=n.useState({sport_id:"",type:"All",sub_type:"All",price:"",...t||{}}),[g,S]=n.useState({}),[$,_]=n.useState(!1),{dispatch:I}=te.useContext(se),[v,B]=n.useState(null);n.useEffect(()=>{w(t?{id:t.id,sport_id:t.sport_id,type:t.type||"All",sub_type:t.sub_type||"All",price:t.price||""}:{sport_id:"",type:"All",sub_type:"All",price:""})},[t]),n.useEffect(()=>{if(m.sport_id&&(f==null?void 0:f.length)>0){const d=f.find(x=>x.id===parseInt(m.sport_id));B(d)}},[m.sport_id,f]);const C=d=>{const{name:x,value:E}=d.target;w(F=>({...F,[x]:x==="price"?E===""?"":parseFloat(E):E})),g[x]&&S(F=>({...F,[x]:""}))},V=()=>{const d={};return m.sport_id||(d.sport_id="Sport is required"),!m.price&&m.price!==0?d.price="Price is required":(isNaN(m.price)||m.price<0)&&(d.price="Price must be a valid number"),S(d),Object.keys(d).length===0},G=async()=>{if(V()){_(!0);try{await h(m),N()}catch(d){j(I,d.message||"Failed to save sport",3e3,"error")}finally{_(!1)}}};if(!r)return null;const q=(f==null?void 0:f.filter(d=>d.status===1))||[];return e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"w-full max-w-md rounded-lg bg-white p-6 shadow-lg",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:u?"Edit Sport Rate":"Add Sport Rate"}),e.jsx("button",{onClick:N,className:"text-gray-500 hover:text-gray-700",children:e.jsx("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:["Sport ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("select",{name:"sport_id",value:m.sport_id,onChange:C,className:`w-full rounded-lg border ${g.sport_id?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sport"}),q.map(d=>e.jsx("option",{value:d.id,children:d.name},d.id))]}),g.sport_id&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:g.sport_id})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Type"}),e.jsxs("select",{name:"type",value:m.type,onChange:C,className:"w-full rounded-lg border border-gray-300 p-2",children:[e.jsx("option",{value:"All",children:"All"}),(M=v==null?void 0:v.sport_types)==null?void 0:M.map((d,x)=>d.type&&d.type.trim()!==""&&e.jsx("option",{value:d.type,children:d.type},x))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Sub-Type"}),e.jsxs("select",{name:"sub_type",value:m.sub_type,onChange:C,className:"w-full rounded-lg border border-gray-300 p-2",children:[e.jsx("option",{value:"All",children:"All"}),(z=v==null?void 0:v.sport_types)==null?void 0:z.filter(d=>d.type===m.type||m.type==="All").flatMap(d=>d.sub_types||[]).filter((d,x,E)=>d&&d.trim()!==""&&E.indexOf(d)===x).map((d,x)=>e.jsx("option",{value:d,children:d},x))]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:["Price ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("input",{type:"number",name:"price",value:m.price,onChange:C,className:`w-full rounded-lg border ${g.price?"border-red-500":"border-gray-300"} p-2`,placeholder:"Enter price",min:"0",step:"0.01"}),g.price&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:g.price})]}),e.jsxs("div",{className:"mt-6 flex justify-end space-x-3",children:[e.jsx("button",{onClick:N,className:"rounded-lg border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx(ae,{onClick:G,loading:$,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/90",children:$?"Saving...":"Save"})]})]})]})})};function Ve({club:r,courts:N}){var X;const{id:h}=Ce(),[t,f]=n.useState(null),{dispatch:u}=te.useContext(se),[m,w]=n.useState(null),[g,S]=n.useState(""),[$,_]=n.useState(!1),[I,v]=n.useState("personal"),[B,C]=n.useState(!0);n.useState([]);const[V,G]=n.useState(!1),[q,M]=n.useState(null),[z,d]=n.useState([]),[x,E]=n.useState([]),[F,re]=n.useState(null),[ie,J]=n.useState(!1),[de,le]=n.useState(null),[R,ne]=n.useState([]),[ce,U]=n.useState(!1),[K,W]=n.useState(null),[oe,pe]=n.useState([]),[me,Y]=n.useState(null),L=async()=>{try{C(!0);const a=await ee.getOne("coach",h,{join:["user|user_id"]}),i=await ee.getList("coach_sports",{filter:[`coach_id,eq,${h}`],join:["sports|sport_id"]});if(r!=null&&r.id)try{const l=await c.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${r.id}`,{},"GET");pe(l.sports||[])}catch(l){console.error("Error fetching club sports:",l)}f(a.model),ne(i.list)}catch(a){console.error(a)}finally{C(!1)}};async function xe(){try{const a=await c.callRawAPI(`/v3/api/custom/courtmatchup/reservations/coach/${r==null?void 0:r.id}?coach_id=${h}`,{},"GET");E(a.booking)}catch(a){console.error(a)}}n.useEffect(()=>{r!=null&&r.id&&h&&xe()},[h,r==null?void 0:r.id]);const he=async a=>{var s;w(a);const o=["first_name","last_name","email","phone"].includes(a)?(s=t==null?void 0:t.user)==null?void 0:s[a]:t==null?void 0:t[a];S(o||"")},ue=async a=>{var i,l;_(!0);try{const s=["first_name","last_name","email","phone"].includes(a);c.setTable(s?"user":"coach"),s?await c.callRestAPI({id:t.user.id,[a]:g},"PUT"):await c.callRestAPI({id:h,[a]:g},"PUT"),await P(c,{user_id:localStorage.getItem("user"),activity_type:T.coach_management,action_type:A.UPDATE,data:{coach_id:t.id,[a]:g},club_id:r==null?void 0:r.id,description:`Updated ${a} for ${(i=t==null?void 0:t.user)==null?void 0:i.first_name} ${(l=t==null?void 0:t.user)==null?void 0:l.last_name}`}),await L(),w(null),j(u,"Updated successfully")}catch(o){console.error(o),j(u,"Error updating field","error")}finally{_(!1)}},ye=()=>{w(null),S("")},ge=a=>{try{if(a.size>2*1024*1024){j(u,"File size exceeds 2MB limit. Please choose a smaller file.",3e3,"error");return}le(a.type);const i=new FileReader;i.onload=()=>{re(i.result),J(!0)},i.readAsDataURL(a)}catch(i){j(u,i==null?void 0:i.message,3e3,"error"),console.log(i)}},_e=async a=>{var i,l;try{_(!0);const o=de==="image/png",s=new File([a],`cropped_profile.${o?"png":"jpg"}`,{type:o?"image/png":"image/jpeg"});let y=new FormData;y.append("file",s);let p=await c.uploadImage(y);c.setTable("user"),await c.callRestAPI({id:h,photo:p==null?void 0:p.url},"PUT"),await P(c,{user_id:localStorage.getItem("user"),activity_type:T.coach_management,action_type:A.UPDATE,data:{coach_id:t.id,photo:p==null?void 0:p.url},club_id:r==null?void 0:r.id,description:`Updated photo for ${(i=t==null?void 0:t.user)==null?void 0:i.first_name} ${(l=t==null?void 0:t.user)==null?void 0:l.last_name}`}),f({...t,photo:p==null?void 0:p.url}),j(u,"Photo updated successfully",3e3,"success")}catch(o){j(u,o==null?void 0:o.message,3e3,"error"),console.log(o)}finally{_(!1)}},je=async()=>{var a,i;try{_(!0),c.setTable("user"),await c.callRestAPI({id:h,photo:null},"PUT"),await P(c,{user_id:localStorage.getItem("user"),activity_type:T.coach_management,action_type:A.UPDATE,data:{coach_id:t.id,photo:null},club_id:r==null?void 0:r.id,description:`Removed photo for ${(a=t==null?void 0:t.user)==null?void 0:a.first_name} ${(i=t==null?void 0:t.user)==null?void 0:i.last_name}`}),f({...t,photo:null}),j(u,"Photo removed successfully",3e3,"success")}catch(l){j(u,l==null?void 0:l.message,3e3,"error"),console.log(l)}finally{_(!1)}},Q=a=>{M(a);const[i,l]=a.time.split(" "),[o]=i.split(":");let s=parseInt(o);l==="PM"&&s!==12?s+=12:l==="AM"&&s===12&&(s=0);const y=`${s.toString().padStart(2,"0")}:00:00`;d([{day:a.date.split(",")[0],time:y}]),G(!0)};n.useEffect(()=>{h&&L()},[h,r==null?void 0:r.id]);const fe=()=>{W(null),U(!0)},ve=a=>{W(a),U(!0)},be=async a=>{var i,l;try{Y(a),_(!0),c.setTable("coach_sports"),await c.callRestAPI({id:a},"DELETE"),await P(c,{user_id:localStorage.getItem("user"),activity_type:T.coach_management,action_type:A.DELETE,data:{coach_id:t.id,coach_sport_id:a},club_id:r==null?void 0:r.id,description:`Deleted sport rate for ${(i=t==null?void 0:t.user)==null?void 0:i.first_name} ${(l=t==null?void 0:t.user)==null?void 0:l.last_name}`}),await L(),j(u,"Sport rate deleted successfully",3e3,"success")}catch(o){console.error(o),j(u,"Error deleting sport rate",3e3,"error")}finally{Y(null),_(!1)}},Ne=async a=>{var i,l,o,s,y,p;try{if(_(!0),a.id){const b=R.find(Se=>Se.id===a.id);b&&parseInt(a.sport_id)!==parseInt(b.sport_id)?(c.setTable("coach_sports"),await c.callRestAPI({id:a.id},"DELETE"),await c.callRestAPI({coach_id:h,sport_id:a.sport_id,type:a.type,sub_type:a.sub_type,price:a.price},"POST"),await P(c,{user_id:localStorage.getItem("user"),activity_type:T.coach_management,action_type:A.UPDATE,data:{coach_id:t.id,old_coach_sport_id:a.id,old_sport_id:b.sport_id,new_sport_id:a.sport_id,type:a.type,sub_type:a.sub_type,price:a.price},club_id:r==null?void 0:r.id,description:`Changed sport and updated rate for ${(i=t==null?void 0:t.user)==null?void 0:i.first_name} ${(l=t==null?void 0:t.user)==null?void 0:l.last_name}`})):(c.setTable("coach_sports"),await c.callRestAPI({id:a.id,sport_id:a.sport_id,type:a.type,sub_type:a.sub_type,price:a.price},"PUT"),await P(c,{user_id:localStorage.getItem("user"),activity_type:T.coach_management,action_type:A.UPDATE,data:{coach_id:t.id,coach_sport_id:a.id,sport_id:a.sport_id,type:a.type,sub_type:a.sub_type,price:a.price},club_id:r==null?void 0:r.id,description:`Updated sport rate for ${(o=t==null?void 0:t.user)==null?void 0:o.first_name} ${(s=t==null?void 0:t.user)==null?void 0:s.last_name}`}))}else c.setTable("coach_sports"),await c.callRestAPI({coach_id:h,sport_id:a.sport_id,type:a.type,sub_type:a.sub_type,price:a.price},"POST"),await P(c,{user_id:localStorage.getItem("user"),activity_type:T.coach_management,action_type:A.CREATE,data:{coach_id:t.id,sport_id:a.sport_id,type:a.type,sub_type:a.sub_type,price:a.price},club_id:r==null?void 0:r.id,description:`Added sport rate for ${(y=t==null?void 0:t.user)==null?void 0:y.first_name} ${(p=t==null?void 0:t.user)==null?void 0:p.last_name}`});await L(),U(!1),j(u,a.id?"Sport rate updated successfully":"Sport rate added successfully",3e3,"success")}catch(b){console.error(b),j(u,"Error saving sport rate",3e3,"error")}finally{_(!1)}},k=(a,i,l,o=!1)=>e.jsx("div",{className:"border-b pb-4",children:m===a?e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("label",{className:"block text-gray-500",children:i}),e.jsx("button",{onClick:ye,className:"text-primaryBlue hover:underline",children:"Cancel"})]}),a==="bio"?e.jsx("textarea",{value:g,onChange:s=>S(s.target.value),className:"mb-3 h-32 w-full rounded-lg border border-gray-300 p-2",placeholder:"Enter your bio..."}):e.jsx("input",{type:"text",value:g,onChange:s=>S(s.target.value),className:"mb-3 w-full rounded-lg border border-gray-300 p-2"}),e.jsx(ae,{loading:$,onClick:()=>ue(a),className:"rounded-lg bg-[#1E335F] px-6 py-2 text-white hover:bg-[#162544]",children:"Save"})]}):e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-gray-500",children:i}),e.jsx("div",{className:"font-medium",children:l}),o&&e.jsxs("p",{className:"text-sm text-gray-400",children:["Your ",i.toLowerCase()," is not shared with other users."]})]}),e.jsx("button",{onClick:()=>he(a),className:"text-blue-600 hover:underline",children:"Edit"})]})}),we=()=>{var a,i,l,o;switch(I){case"personal":return e.jsxs("div",{className:"space-y-4",children:[k("first_name","First Name",(a=t==null?void 0:t.user)==null?void 0:a.first_name),k("last_name","Last name",(i=t==null?void 0:t.user)==null?void 0:i.last_name),k("email","Email",(l=t==null?void 0:t.user)==null?void 0:l.email,!0),k("phone","Phone number",(o=t==null?void 0:t.user)==null?void 0:o.phone,!0),k("bio","Bio",(t==null?void 0:t.bio)||"No bio provided"),e.jsx("div",{className:"border-b pb-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("label",{className:"block text-gray-500",children:"Sport-specific Hourly Rates"}),e.jsxs("button",{onClick:fe,className:"flex items-center gap-1 rounded-lg bg-primaryBlue px-3 py-1 text-sm text-white hover:bg-primaryBlue/90",children:[e.jsx($e,{className:"h-4 w-4"}),"Add Rate"]})]}),R&&R.length>0?e.jsx("div",{className:"mt-2 space-y-2",children:R.map(s=>{var y;return e.jsxs("div",{className:"flex items-center gap-2 rounded-lg bg-gray-50 p-2",children:[e.jsx("span",{className:"font-medium",children:(y=s.sports)==null?void 0:y.name}),s.type&&s.type!=="All"&&e.jsxs("span",{className:"text-gray-500",children:["(",s.type,")"]}),s.sub_type&&s.sub_type!=="All"&&e.jsxs("span",{className:"text-gray-500",children:["- ",s.sub_type]}),e.jsx("span",{className:"ml-auto font-medium text-gray-900",children:Ae(s.price)}),e.jsxs("div",{className:"ml-2 flex gap-1",children:[e.jsx("button",{onClick:()=>ve(s),className:"rounded p-1 text-gray-500 hover:bg-gray-200",title:"Edit",children:e.jsx(H,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>be(s.id),className:"rounded p-1 text-red-500 hover:bg-red-50",title:"Delete",disabled:me===s.id,children:e.jsx(Fe,{className:"h-4 w-4"})})]})]},s.id)})}):e.jsx("div",{className:"font-medium",children:"No sport-specific rates defined"})]})})})]});case"lessons":return e.jsxs("div",{className:"overflow-x-auto",children:[e.jsx("div",{className:"hidden md:block",children:e.jsxs("table",{className:"min-w-full border-separate border-spacing-y-2",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"text-left text-sm text-gray-500",children:[e.jsx("th",{className:"px-4 pb-4",children:"Date"}),e.jsx("th",{className:"px-4 pb-4",children:"Event"}),e.jsx("th",{className:"px-4 pb-4",children:"Court"}),e.jsx("th",{className:"px-4 pb-4",children:"Hours of coaching"}),e.jsx("th",{className:"px-4 pb-4",children:"Players"}),e.jsx("th",{className:"px-4 pb-4",children:"Coaches"}),e.jsx("th",{className:"px-4 pb-4"})]})}),e.jsx("tbody",{children:x.map((s,y)=>{var p;return e.jsxs("tr",{className:"overflow-hidden",children:[e.jsx("td",{className:"rounded-l-xl bg-gray-100 px-4 py-3",children:e.jsxs("div",{className:"text-sm text-gray-500",children:[D(s.booking_date).format("dddd, MMMM D")," |"," ",O(s==null?void 0:s.start_time)," -"," ",O(s==null?void 0:s.end_time)]})}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:s==null?void 0:s.booking_type}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:s!=null&&s.court_id?`#${s==null?void 0:s.court_id}`:"N/A"}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:s==null?void 0:s.duration}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:(p=JSON.parse(s==null?void 0:s.player_ids))==null?void 0:p.length}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:"1"}),e.jsx("td",{className:"rounded-r-xl bg-gray-100 px-4 py-3",children:e.jsx("button",{className:"text-gray-400 hover:text-gray-500",onClick:()=>Q(s),children:e.jsx(H,{className:"h-5 w-5"})})})]},y)})})]})}),e.jsx("div",{className:"space-y-4 md:hidden",children:x.map((s,y)=>{var p,b;return e.jsx("div",{className:"rounded-xl bg-gray-100 p-4",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"text-sm text-gray-500",children:[D(s.booking_date).format("dddd, MMMM D")," |"," ",O(s==null?void 0:s.start_time)," -"," ",O(s==null?void 0:s.end_time)]}),e.jsx("div",{className:"font-medium",children:s==null?void 0:s.event}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Court:"," ",(p=N==null?void 0:N.find(Z=>Z.id==(s==null?void 0:s.court_id)))==null?void 0:p.name]}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Duration: ",s==null?void 0:s.duration]}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Players: ",(b=JSON.parse(s==null?void 0:s.player_ids))==null?void 0:b.length]}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Coach: ",s==null?void 0:s.coach_id]})]}),e.jsx("button",{className:"text-gray-400 hover:text-gray-500",onClick:()=>Q(s),children:e.jsx(H,{className:"h-5 w-5"})})]})},y)})}),x.length===0&&e.jsx("div",{className:"flex w-full items-center justify-center text-center text-sm text-gray-500",children:"No lessons found"})]});default:return null}};return e.jsxs("div",{children:[$||B?e.jsx(Ee,{}):null,e.jsxs("div",{className:"mx-auto max-w-5xl",children:[e.jsx("h1",{className:"mb-6 text-2xl font-bold capitalize",children:!(t!=null&&t.first_name)||!(t!=null&&t.last_name)?"Coach's profile":`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}'s profile`}),e.jsx(Ie,{isOpen:ie,onClose:()=>J(!1),image:F,onCropComplete:_e}),e.jsx(ke,{isOpen:ce,onClose:()=>U(!1),onSave:Ne,sportData:K,clubSports:oe,isEditing:!!K}),e.jsx("div",{className:"mb-6 border-b",children:e.jsxs("div",{className:"flex gap-8",children:[e.jsx("button",{onClick:()=>v("personal"),className:`pb-4 ${I==="personal"?"border-b-2 border-[#1E335F] font-medium text-[#1E335F]":"text-gray-500"}`,children:"Personal information"}),e.jsx("button",{onClick:()=>v("lessons"),className:`pb-4 ${I==="lessons"?"border-b-2 border-[#1E335F] font-medium text-[#1E335F]":"text-gray-500"}`,children:"Lessons"})]})}),e.jsx("div",{className:"rounded-lg bg-white p-6 shadow",children:e.jsxs("div",{className:"space-y-6",children:[I==="personal"&&e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex items-end gap-4",children:[e.jsx("img",{src:((X=t==null?void 0:t.user)==null?void 0:X.photo)||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-1",children:"Upload Image"}),e.jsx("p",{className:"my-2 text-sm text-gray-600",children:"Min 400x400px, PNG or JPEG"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{disabled:!(t!=null&&t.photo),className:"rounded-lg border border-red-600 px-2 py-1 text-red-600 disabled:opacity-50",onClick:je,children:"Remove"}),e.jsxs("label",{className:"cursor-pointer rounded-lg border border-gray-300 px-2 py-1 text-gray-500",children:[e.jsx("input",{type:"file",accept:".jpg,.jpeg,.png",className:"hidden",onChange:a=>{const i=a.target.files[0];i&&ge(i)}}),"Change Photo"]})]})]})]})}),we()]})})]})]})}export{Ve as V};
