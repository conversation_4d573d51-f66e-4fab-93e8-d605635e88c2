import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{M as R}from"./MembershipCard-13437303.js";import{r as a,f as O,b as V}from"./vendor-851db8c1.js";import{n as $,o as z,G as K,m as q,e as H,v as f,d as Q,M as W,b as x,t as N}from"./index-ca7cbd3e.js";import{B as X}from"./BackButton-11ba52b2.js";import{b as Y,c as Z}from"./index.esm-c561e951.js";import{S as ee}from"./index.esm-92169588.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const se=c=>{const m={size:24,className:"text-white"},o={Visa:{icon:e.jsx(Y,{...m}),bgColor:"bg-[#1A1F71]"},Mastercard:{icon:e.jsx(Z,{...m}),bgColor:"bg-[#EB001B]"},"American Express":{icon:e.jsx(ee,{...m}),bgColor:"bg-[#006FCF]"},Discover:{text:"DISC",bgColor:"bg-[#FF6000]"},"Diners Club":{icon:e.jsx($,{...m}),bgColor:"bg-[#0069AA]"},JCB:{icon:e.jsx(z,{...m}),bgColor:"bg-[#0B4EA2]"},UnionPay:{text:"UP",bgColor:"bg-[#00447C]"}}[c];return o?e.jsx("div",{className:`flex h-8 w-12 items-center justify-center rounded ${o.bgColor} text-white`,children:o.icon||e.jsx("span",{className:"text-sm font-bold",children:o.text})}):null};let d=new W;function Je(){var C;const[c,m]=a.useState([]),[v,o]=a.useState(!1),[M,te]=a.useState(null),[t,A]=a.useState(null),[j,S]=a.useState(1),[i,B]=a.useState(null),[_,w]=a.useState(!1),h=O(),{dispatch:l}=V.useContext(K),g=localStorage.getItem("user"),[u,F]=a.useState(null),[b,k]=a.useState(null),D=async()=>{var s,r,y;o(!0);try{d.setTable("user");const p=await d.callRestAPI({id:g},"GET");d.setTable("clubs");const n=await d.callRestAPI({id:(s=p==null?void 0:p.model)==null?void 0:s.club_id},"GET");F(n.model),console.log("view model response",(r=n==null?void 0:n.model)==null?void 0:r.membership_settings),m(JSON.parse((y=n==null?void 0:n.model)==null?void 0:y.membership_settings)||[])}catch(p){console.log(p)}finally{o(!1)}};async function T(){var s;try{o(!0);const{data:r,limit:y,error:p,message:n}=await d.getCustomerStripeCards();if(console.log(r),p&&x(l,n,5e3),!r)return;const U=(s=r==null?void 0:r.data)==null?void 0:s.find(J=>{var E,I;return J.id===((I=(E=r==null?void 0:r.data[0])==null?void 0:E.customer)==null?void 0:I.default_source)});B(U)}catch(r){console.error("ERROR",r),x(l,r.message,5e3),N(dispatch,r.code)}finally{o(!1)}}a.useEffect(()=>{D(),T(),q({title:"Membership",path:"/user/membership",clubName:u==null?void 0:u.name,favicon:u==null?void 0:u.club_logo,description:"Membership"})},[]);const L=s=>{A(s),S(2)},P=async()=>{w(!0);try{if(b!=null&&b.subId){const s=await d.changeStripeSubscription({userId:g,activeSubscriptionId:b.subId,newPlanId:t.plan_id});s.error?(console.error(s.message),x(l,s.message,7500,"error")):(x(l,"Subscription updated successfully",3e3),h("/user/profile?tab=membership"))}else{const s=await d.createStripeSubscription({planId:t.plan_id});s.error?(console.error(s.message),x(l,s.message,7500,"error")):(x(l,"Subscription created successfully",3e3),h("/user/profile?tab=membership"))}}catch(s){console.error("Error",s),x(l,s.message,7500,"error"),N(l,s.code)}finally{w(!1)}};async function G(){try{const s=await d.getCustomerStripeSubscription(g);k(s.customer)}catch(s){console.error(s),N(dispatch,s.code)}}return a.useEffect(()=>{G()},[g]),e.jsxs("div",{className:"mx-auto max-w-6xl p-6",children:[v&&e.jsx(H,{}),e.jsx(X,{onBack:()=>{j===1?h(-1):S(1)}}),j===1&&e.jsxs(e.Fragment,{children:[e.jsx("div",{children:e.jsx("h1",{className:"mb-5 text-center text-2xl font-medium text-primaryBlue md:text-4xl",children:"Membership"})}),e.jsx("div",{className:"mt-4 grid grid-cols-1 gap-4 md:grid-cols-3",children:(c==null?void 0:c.length)>0&&(c==null?void 0:c.map(s=>e.jsx(R,{...s,isCurrentPlan:M===s.id,onSelect:()=>L(s),isActive:b.planId===s.plan_id},s.plan_name)))})]}),j===2&&e.jsxs("div",{className:"mx-auto mt-10 flex max-w-5xl gap-10",children:[e.jsxs("div",{className:"h-fit flex-1 rounded-xl bg-white shadow-lg",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Membership details"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"mb-6 flex items-center justify-between",children:e.jsx("h3",{className:"text-xl font-medium",children:t==null?void 0:t.plan_name})}),e.jsx("div",{className:"border-b border-gray-200 "}),e.jsxs("div",{className:"mt-2 text-2xl font-bold",children:[f(t==null?void 0:t.price),e.jsx("span",{className:"text-base font-normal text-gray-600",children:"/month"})]}),e.jsx("div",{className:"mt-4 space-y-2",children:(C=t==null?void 0:t.features)==null?void 0:C.map(s=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"rounded-full bg-gray-100 p-1",children:"⚾"}),e.jsx("span",{className:"text-gray-600",children:s.text})]},s))})]})})]}),e.jsxs("div",{className:"h-fit flex-1 rounded-xl bg-white shadow-lg",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Payment details"})}),e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex justify-between text-gray-600",children:e.jsx("span",{children:"FEES"})}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Plan price"}),e.jsx("span",{children:f(t==null?void 0:t.price)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Taxes"}),e.jsx("span",{children:f(.97)})]}),e.jsx("div",{className:"border-t pt-4",children:e.jsxs("div",{className:"flex justify-between font-medium",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{children:f((t==null?void 0:t.price)+.97)})]})})]})}),e.jsx("div",{className:"mb-6",children:i?e.jsxs("div",{className:"flex items-center justify-between rounded-lg border border-gray-200 p-3",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[se(i==null?void 0:i.brand),e.jsxs("p",{className:"text-black",children:[i==null?void 0:i.brand," • ",i==null?void 0:i.last4]})]}),e.jsx("button",{onClick:()=>{h("/user/profile?tab=payment-methods")},className:"text-gray-600 hover:text-gray-800",children:"Change"})]}):e.jsxs("div",{className:"rounded-lg border border-red-200 bg-red-50 p-4",children:[e.jsx("p",{className:"mb-2 text-red-600",children:"Please add a payment method to continue with the subscription"}),e.jsx("button",{onClick:()=>{h("/user/profile?tab=payment-methods")},className:"text-sm font-medium text-primaryBlue hover:text-blue-700",children:"Add Payment Method"})]})}),e.jsx(Q,{onClick:P,loading:_,disabled:!i,className:`w-full rounded-lg py-3 text-center text-white ${i?"bg-primaryBlue hover:bg-blue-700":"cursor-not-allowed bg-gray-400"}`,children:i?"Pay now":"Add payment method to continue"}),e.jsx("p",{className:"mt-4 text-sm text-gray-500",children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur erat nisi, porta a ipsum eu, accumsan dapibus enim. Donec ultrices congue libero in convallis. Cras condimentum felis eget dignissim tincidunt."})]})]})]})]})}export{Je as default};
