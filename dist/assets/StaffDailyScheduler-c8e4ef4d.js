import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{r as o,b as ml}from"./vendor-851db8c1.js";import{u as pl,c as xl,q as X,K as gl,ah as hl,U as fl,ai as bl,M as yl,T as wl,i as kl}from"./index-ca7cbd3e.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const Y=new yl,jl=new wl;function ee(){var D,T,$;const[Z,v]=o.useState([]),[g,y]=o.useState(null),[ll,w]=ml.useState(!1),[el,k]=o.useState(!1),[t,j]=o.useState(null),[a,h]=o.useState(null),[p,x]=o.useState(null),[_,r]=o.useState(!1),[N,S]=o.useState(!1),[sl,tl]=o.useState([]),[C,E]=o.useState(""),{club:c,sports:i,courts:u}=pl();async function n({filters:e}){w(!0);try{console.log("filters",e);const f=(await jl.getList("reservation",{filter:[`courtmatchup_reservation.club_id,cs,${c==null?void 0:c.id}`,...e],join:["booking|booking_id","user|user_id","buddy|buddy_id","clubs|club_id"]})).list.map(l=>{var L,F,M,O,A,B,I,R,z,K,U,H,J,Q,q,G,P,W,V;return{id:(L=l==null?void 0:l.booking)==null?void 0:L.id,court_ids:(F=l==null?void 0:l.booking)!=null&&F.court_ids?JSON.parse((M=l==null?void 0:l.booking)==null?void 0:M.court_ids):(O=l==null?void 0:l.booking)==null?void 0:O.court_id,startTime:(A=l==null?void 0:l.booking)==null?void 0:A.start_time,endTime:(B=l==null?void 0:l.booking)==null?void 0:B.end_time,date:(I=l==null?void 0:l.booking)==null?void 0:I.date,reservation_type:(R=l==null?void 0:l.booking)==null?void 0:R.reservation_type,title:(z=kl.find(b=>{var m;return b.value==((m=l==null?void 0:l.booking)==null?void 0:m.reservation_type)}))==null?void 0:z.label,status:(K=l==null?void 0:l.booking)==null?void 0:K.status,price:(U=l==null?void 0:l.booking)==null?void 0:U.price,duration:(H=l==null?void 0:l.booking)==null?void 0:H.duration,players:(J=l==null?void 0:l.booking)!=null&&J.player_ids?JSON.parse((Q=l==null?void 0:l.booking)==null?void 0:Q.player_ids):[],sport:i.find(b=>{var m;return b.id==((m=l==null?void 0:l.booking)==null?void 0:m.sport_id)}),user_id:(q=l==null?void 0:l.booking)==null?void 0:q.user_id,booking:l==null?void 0:l.booking,user:l==null?void 0:l.user,buddy:l==null?void 0:l.buddy,club:l==null?void 0:l.clubs,type:(G=l==null?void 0:l.booking)==null?void 0:G.type,sub_type:(P=l==null?void 0:l.booking)==null?void 0:P.subtype,court_fee:(W=l==null?void 0:l.booking)==null?void 0:W.court_fee,sport_id:(V=l==null?void 0:l.booking)==null?void 0:V.sport_id}});v(f)}catch(d){console.error("Error fetching data:",d)}finally{w(!1)}}const cl=e=>{j(e),h(null),x(null);const d=`courtmatchup_booking.sport_id,cs,${e==null?void 0:e.id}`;n({filters:[d]})},dl=e=>{h(e),x(null),r(!1);const d=[`courtmatchup_booking.sport_id,cs,${t==null?void 0:t.id}`,`courtmatchup_booking.type,cs,${e}`];n({filters:d})},ol=e=>{x(e),S(!1);const d=[`courtmatchup_booking.sport_id,cs,${t==null?void 0:t.id}`,`courtmatchup_booking.type,cs,${a}`,`courtmatchup_booking.subtype,cs,${e}`];n({filters:d})},il=async()=>{try{Y.setTable("user");const e=await Y.callRestAPI({},"GETALL");tl(e.list),console.log("users",e)}catch(e){console.log(e)}};o.useEffect(()=>{c!=null&&c.id&&(n({filters:[]}),il())},[c==null?void 0:c.id]);const nl=()=>{k(!0)},al=e=>{y(e)},ul=()=>{j(null),h(null),x(null),E(""),n({filters:[]})};return s.jsxs("div",{className:"h-full rounded-lg bg-white p-4",children:[s.jsx("div",{className:"flex w-full flex-col",children:s.jsxs("div",{className:"mb-6 flex flex-col space-y-4 lg:flex-row lg:items-start lg:justify-between lg:space-y-0",children:[s.jsx("div",{className:"w-full lg:w-auto lg:min-w-[300px] lg:max-w-[400px]",children:s.jsxs("div",{className:"relative flex items-center",children:[s.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:s.jsx(xl,{className:"h-5 w-5 text-gray-500"})}),s.jsx("input",{type:"text",value:C,onChange:e=>{E(e.target.value);const d=t?[`courtmatchup_booking.sport_id,cs,${t==null?void 0:t.id}`]:[],f=e.target.value?[`courtmatchup_user.first_name,cs,${e.target.value}`]:[];n({filters:[...d,...f]})},className:"block w-full rounded-md border border-gray-200 py-2 pl-10 pr-4 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search players by name..."})]})}),s.jsxs("div",{className:"flex flex-col space-y-4 lg:flex-row lg:items-start lg:space-x-4 lg:space-y-0",children:[s.jsxs("div",{className:"flex flex-col space-y-3",children:[s.jsx("div",{className:"flex max-w-fit flex-wrap gap-2 rounded-lg border bg-white p-2 sm:flex-nowrap sm:gap-0 sm:overflow-hidden sm:p-0",children:(i==null?void 0:i.length)>0&&(i==null?void 0:i.map((e,d)=>s.jsx("button",{className:`whitespace-nowrap rounded px-3 py-2 text-sm font-medium transition-colors sm:rounded-none ${(t==null?void 0:t.id)===(e==null?void 0:e.id)?"bg-primaryBlue text-white":"text-gray-600 hover:bg-gray-100"}`,onClick:()=>cl(e),children:e==null?void 0:e.name},d)))}),s.jsxs("div",{className:"flex flex-wrap gap-2 sm:flex-nowrap sm:space-x-2",children:[t&&s.jsxs("div",{className:"relative min-w-[140px]",children:[s.jsxs("button",{onClick:()=>r(!_),className:"flex w-full items-center justify-between space-x-2 rounded-lg border bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:[s.jsx("span",{className:"truncate",children:a||"Select Type"}),s.jsx(X,{className:"h-4 w-4 flex-shrink-0"})]}),_&&s.jsx("div",{className:"absolute left-0 z-10 mt-1 w-full min-w-[180px] rounded-lg border bg-white shadow-lg",children:(D=t==null?void 0:t.sport_types)==null?void 0:D.map((e,d)=>s.jsx("button",{className:"block w-full px-4 py-2 text-left text-sm hover:bg-gray-100",onClick:()=>dl(e.type),children:e.type},d))})]}),a&&s.jsxs("div",{className:"relative min-w-[140px]",children:[s.jsxs("button",{onClick:()=>S(!N),className:"flex w-full items-center justify-between space-x-2 rounded-lg border bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:[s.jsx("span",{className:"truncate",children:p||"Select Subtype"}),s.jsx(X,{className:"h-4 w-4 flex-shrink-0"})]}),N&&s.jsx("div",{className:"absolute left-0 z-10 mt-1 w-full min-w-[180px] rounded-lg border bg-white shadow-lg",children:($=(T=t==null?void 0:t.sport_types)==null?void 0:T.find(e=>e.type===a))==null?void 0:$.subtype.map((e,d)=>s.jsx("button",{className:"block w-full px-4 py-2 text-left text-sm hover:bg-gray-100",onClick:()=>ol(e),children:e},d))})]})]})]}),s.jsxs("div",{className:"flex flex-wrap gap-2 sm:flex-nowrap sm:space-x-2",children:[(C||t||a||p)&&s.jsxs("button",{onClick:ul,className:"flex items-center space-x-2 rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50",children:[s.jsx(gl,{className:"h-4 w-4"}),s.jsx("span",{className:"hidden sm:inline",children:"Clear filters"}),s.jsx("span",{className:"sm:hidden",children:"Clear"})]}),s.jsxs("button",{className:"flex items-center space-x-2 rounded-lg border bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:[s.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),s.jsx("span",{className:"hidden sm:inline",children:"History"}),s.jsx("span",{className:"sm:hidden",children:"Hist"})]}),s.jsxs("button",{onClick:nl,className:"hover:bg-navy-800 flex items-center space-x-2 rounded-lg bg-navy-700 px-3 py-2 text-sm font-medium text-white",children:[s.jsx("span",{children:"+"}),s.jsx("span",{children:"Add event"})]})]})]})]})}),s.jsx(hl,{sports:i,events:Z,loading:ll,onDateChange:n,onEventClick:al,courts:(u==null?void 0:u.filter(e=>!(t&&e.sport_id!==t.id||a&&e.type!==a||p&&e.sub_type!==p)))||[],fetchData:n}),g&&s.jsx(fl,{isOpen:!!g,onClose:()=>y(null),event:g,courts:u||[],users:sl,clubId:c==null?void 0:c.id,sports:i,fetchData:n,club:c}),(c==null?void 0:c.id)&&s.jsx(bl,{setShowEventModal:k,showEventModal:el,fetchData:n,club:c,sports:i,courts:u})]})}export{ee as default};
