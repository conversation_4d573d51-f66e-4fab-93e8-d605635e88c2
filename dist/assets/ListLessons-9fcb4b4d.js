import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as i,b as u,f as Me}from"./vendor-851db8c1.js";import{M as Ne,T as _e,G as Pe,_ as ve,ap as Ae,$ as Fe,c as Ee,v as Se,d as ke,a0 as Be,aq as Re,E as Oe,D as Ie,H as He,b as G,A as Ve,i as Te,U as Ze,V as ze,t as Ge,W as qe,X as De,Y as ye,Z as Ue}from"./index-ca7cbd3e.js";import{c as Je,a as ae}from"./yup-54691517.js";import{u as Ke}from"./react-hook-form-687afde5.js";import{o as Ye}from"./yup-2824f222.js";import{P as Qe}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import We from"./Skeleton-1e8bf077.js";import{C as Xe}from"./CheckinModal-a9c18ddc.js";import{L as es}from"./LoadingOverlay-87926629.js";import{P as ss,R as ts,T as as,F as ns}from"./ReservationStatus-0d38d99f.js";import{D as rs}from"./DataTable-a2248415.js";import"./numeral-ea653b2a.js";import{H as is}from"./HistoryComponent-cf25af7a.js";import{B as ls}from"./BottomDrawer-f2e7890f.js";import{b as os}from"./index.esm-b72032a7.js";import{B as ds}from"./BackButton-11ba52b2.js";import{T as ms}from"./TimeSlots-c9c10356.js";import{C as cs}from"./Calendar-282b3fcf.js";import{S as us}from"./SportTypeSelection-295d9596.js";import{L as Le}from"./ReservationSummary-8fd7b9ef.js";import{f as xs}from"./date-fns-07266b7d.js";new Ne;new _e;let ne=new Ne;new _e;const gs=({isOpen:j,onClose:m,players:q,sports:$,club:l})=>{var T,b,w,D,z;i.useState("selection");const[P,re]=i.useState(null);i.useState(null),i.useState(null);const[y,h]=i.useState(null),[E,U]=i.useState(new Date);i.useState(null);const[C,ie]=i.useState(0),[J,S]=i.useState(0),[O,le]=i.useState(null),[I,oe]=i.useState(null),{state:be,dispatch:R}=i.useContext(Pe),[K,M]=i.useState(!1),[H,de]=i.useState([]),[N,A]=i.useState("main"),[_,F]=i.useState(""),[o,Y]=i.useState([]),[v,me]=i.useState([]),[ce,ue]=i.useState([]),[a,xe]=i.useState(null),[Q,W]=i.useState(!1),[V,we]=i.useState(!0),{start_time:X,end_time:ee,duration:ge}=ve(v),g=localStorage.getItem("role");i.useEffect(()=>{},[N]);const fe=async()=>{W(!0),xs(new Date(y),"yyyy-MM-dd");try{ne.setTable("coach");const s=await ne.callRestAPI({filter:[`courtmatchup_coach.club_id,eq,${l==null?void 0:l.id}`],join:["user|user_id"]},"GETALL");if(!s.error){if(s.list.length===0){G(R,"No coaches found for the selected time slot",4e3,"error");return}ue(s.list),A("coaches")}}catch(s){console.error("ERROR",s),G(R,s.message,5e3)}finally{W(!1)}},pe=()=>{const s=new Date(E);s.setMonth(s.getMonth()-1),U(s)},he=()=>{const s=new Date(E);s.setMonth(s.getMonth()+1),U(s)},se=s=>{h(s)},Z=async()=>{M(!0);try{const s={sport_id:P,type:O,sub_type:I,date:y,player_ids:o.map(c=>c.id),start_time:X,end_time:ee,price:J,duration:ge,coach_id:a==null?void 0:a.id,reservation_type:Re.lesson,club_id:l==null?void 0:l.id,buddy_request:0,payment_intent:null,buddy_details:null,payment_details:null};console.log("form data",s);const n=await ne.callRawAPI(`/v3/api/custom/courtmatchup/${g}/reservations`,s,"POST");await Oe(ne,{user_id:localStorage.getItem("user"),activity_type:Ie.lesson,action_type:He.CREATE,data:s,club_id:l==null?void 0:l.id,description:"Created a lesson reservation"}),n.error||(G(R,"Lesson created successfully",3e3,"success"),m())}catch(s){console.error(s),G(R,s.message||"Error creating lesson",3e3,"error")}finally{M(!1)}},je=s=>{me([{from:s.from,until:s.until}])},t=s=>{Y(n=>n.some(p=>p.id===s.id)?n.filter(p=>p.id!==s.id):[...n,s])},r=s=>o.some(n=>n.id===s),d=q.filter(s=>`${s.first_name} ${s.last_name}`.toLowerCase().includes(_.toLowerCase())),f=()=>{fe()},x=()=>{if(!a){G(R,"Please select a coach",3e3,"error");return}A("players")},k=ce.filter(s=>{var c,p;return`${(s==null?void 0:s.first_name)||((c=s==null?void 0:s.user)==null?void 0:c.first_name)} ${(s==null?void 0:s.last_name)||((p=s==null?void 0:s.user)==null?void 0:p.last_name)}`.toLowerCase().includes(_.toLowerCase())});return i.useEffect(()=>{if(a&&v.length>0){const s=v.reduce((c,p)=>{const L=new Date(`2000/01/01 ${p.from}`),te=(new Date(`2000/01/01 ${p.until}`)-L)/(1e3*60*60);return c+te},0),n=Ae({hourlyRate:a==null?void 0:a.hourly_rate,hours:s,playerCount:o.length||1,feeSettings:l==null?void 0:l.fee_settings});ie(n.total/(o.length||1)),S(n.total)}else C&&(o!=null&&o.length)?S(C*(o==null?void 0:o.length)):S(C)},[C,o,a,v,l==null?void 0:l.fee_settings]),e.jsxs(ls,{isOpen:j,onClose:m,title:N==="main"?"Add New Lesson":N==="coaches"?"Select Coach":"Select Players",showActions:!1,saveLabel:"Create Lesson",onSave:Z,isSubmitting:K,children:[e.jsx(ds,{onBack:()=>{N=="main"?m():A("main")}}),N==="main"?e.jsx("div",{className:" p-4",children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsx(us,{sports:$,onSelectionChange:({sport:s,type:n,subType:c})=>{re(s),le(n),oe(c)}}),e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:e.jsx(cs,{currentMonth:E,selectedDate:y,onDateSelect:se,onPreviousMonth:pe,onNextMonth:he,allowPastDates:!1,minDate:new Date,maxDate:new Date(new Date().setMonth(new Date().getMonth()+3)),daysOff:l!=null&&l.days_off?JSON.parse(l.days_off):[]})}),y&&e.jsx(ms,{onTimeClick:je,selectedDate:y,isLoading:Q,timeRange:H,timeSlots:Fe(),onNext:f,nextButtonText:"Next: Select Coach",startHour:0,endHour:24,interval:30,className:"h-fit rounded-lg bg-white p-4 shadow-5",multipleSlots:!1,individualSelection:!0,isTimeSlotAvailable:s=>!0,clubTimes:l!=null&&l.times?JSON.parse(l.times):[]})]})})})}):N==="coaches"?e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsx(Le,{selectedSport:P,sports:$,selectedType:O,selectedSubType:I,selectedDate:y,selectedTimes:v,selectedCoach:a,timeRange:ve(v)}),e.jsxs("div",{className:"col-span-2 h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Select a Coach"})}),e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"flex items-center gap-1 rounded-lg border border-gray-300 px-2 focus:border-blue-500 focus:outline-none",children:[e.jsx(Ee,{className:"text-gray-500"}),e.jsx("input",{type:"text",placeholder:"Search coaches",value:_,onChange:s=>F(s.target.value),className:"w-full border-none bg-transparent py-2 focus:outline-none focus:ring-0"})]})}),e.jsx("button",{onClick:()=>we(!V),className:"ml-2 flex items-center gap-1 rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:"A-Z"}),e.jsx(os,{className:`text-xs transition-transform ${V?"":"rotate-180"}`})]})})]}),e.jsx("div",{className:"max-h-[calc(90vh-200px)] space-y-4 overflow-y-auto",children:k.length>0?k.sort((s,n)=>{var L,B,te,Ce;const c=`${(s==null?void 0:s.first_name)||((L=s==null?void 0:s.user)==null?void 0:L.first_name)} ${(s==null?void 0:s.last_name)||((B=s==null?void 0:s.user)==null?void 0:B.last_name)}`.toLowerCase(),p=`${(n==null?void 0:n.first_name)||((te=n==null?void 0:n.user)==null?void 0:te.first_name)} ${(n==null?void 0:n.last_name)||((Ce=n==null?void 0:n.user)==null?void 0:Ce.last_name)}`.toLowerCase();return V?c.localeCompare(p):p.localeCompare(c)}).map(s=>{var n,c,p,L,B;return e.jsxs("div",{className:`flex cursor-pointer items-center justify-between rounded-lg border p-3 ${(a==null?void 0:a.id)===s.id?"border-primaryBlue bg-blue-50":"border-gray-100 hover:bg-gray-50"}`,onClick:()=>xe(s),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:(s==null?void 0:s.photo)||((n=s==null?void 0:s.user)==null?void 0:n.photo)||"/default-avatar.png",alt:`${(s==null?void 0:s.first_name)||((c=s==null?void 0:s.user)==null?void 0:c.first_name)} ${(s==null?void 0:s.last_name)||((p=s==null?void 0:s.user)==null?void 0:p.last_name)}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsx("div",{className:"flex flex-col",children:e.jsxs("span",{className:"font-medium capitalize",children:[(s==null?void 0:s.first_name)||((L=s==null?void 0:s.user)==null?void 0:L.first_name)," ",(s==null?void 0:s.last_name)||((B=s==null?void 0:s.user)==null?void 0:B.last_name)]})})]}),e.jsxs("span",{className:"text-gray-600",children:[Se(s.hourly_rate),"/h"]})]},s.id)}):e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No coaches found"})}),e.jsx("div",{className:"mt-6",children:e.jsx(ke,{loading:Q,onClick:x,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",disabled:!a,children:"Next: Select Players"})})]})]})]}):e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsx(Le,{selectedSport:P,sports:$,selectedType:O,selectedSubType:I,selectedDate:y,selectedTimes:v,selectedCoach:a,timeRange:ve(v)}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reservation for:"})}),e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex items-center gap-1 rounded-lg border border-gray-300 px-2 focus:border-blue-500 focus:outline-none",children:[e.jsx("span",{className:"w-5",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),e.jsx("input",{type:"text",placeholder:"search by name",value:_,onChange:s=>F(s.target.value),className:"w-full border-none bg-transparent focus:outline-none focus:ring-0"})]})}),o.length>0&&e.jsx("div",{className:"mb-4 flex flex-wrap gap-2",children:o.map(s=>e.jsxs("div",{className:"flex items-center gap-2 rounded-lg bg-gray-100 px-3 py-1",children:[e.jsx("div",{className:"h-6 w-6 overflow-hidden rounded-full bg-gray-200",children:e.jsx("img",{src:(s==null?void 0:s.photo)||"/default-avatar.png",alt:`${s.first_name} ${s.last_name}`,className:"h-full w-full object-cover"})}),e.jsx("span",{className:"text-sm",children:`${s.first_name} ${s.last_name}`}),e.jsx("button",{onClick:n=>{n.stopPropagation(),t(s)},className:"text-gray-400 hover:text-gray-600",children:e.jsx(Be,{className:"h-4 w-4"})})]},s.id))}),e.jsx("div",{className:"max-h-64 space-y-2 overflow-y-auto rounded-xl bg-gray-50",children:d.length>0?d.map(s=>e.jsxs("div",{onClick:()=>t(s),className:"flex cursor-pointer items-center space-x-3 rounded-lg p-2 hover:bg-gray-50",children:[e.jsx("input",{type:"checkbox",checked:r(s.id),onChange:()=>{},className:"h-4 w-4 rounded border-gray-300 text-blue-600"}),e.jsx("div",{className:"h-8 w-8 overflow-hidden rounded-full bg-gray-200",children:e.jsx("img",{src:(s==null?void 0:s.photo)||"/default-avatar.png",alt:`${s.first_name} ${s.last_name}`,className:"h-full w-full object-cover"})}),e.jsx("span",{children:`${s.first_name} ${s.last_name}`})]},s.id)):e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No players found"})})]})]}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reserving details"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-6",children:[a&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm text-gray-500",children:"COACH"}),e.jsxs("div",{className:"mt-1 flex items-center gap-2",children:[e.jsx("div",{className:"h-6 w-6 overflow-hidden rounded-full bg-gray-200",children:e.jsx("img",{src:(a==null?void 0:a.photo)||((T=a==null?void 0:a.user)==null?void 0:T.photo)||"/default-avatar.png",alt:`${(a==null?void 0:a.first_name)||((b=a==null?void 0:a.user)==null?void 0:b.first_name)} ${(a==null?void 0:a.last_name)||((w=a==null?void 0:a.user)==null?void 0:w.last_name)}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{className:"text-sm",children:[(a==null?void 0:a.first_name)||((D=a==null?void 0:a.user)==null?void 0:D.first_name)," ",(a==null?void 0:a.last_name)||((z=a==null?void 0:a.user)==null?void 0:z.last_name)]})]})]}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm text-gray-500",children:["PLAYERS (",o.length,")"]}),e.jsx("div",{className:"mt-1",children:o.map(s=>e.jsxs("div",{className:"text-sm",children:[s.first_name," ",s.last_name]},s.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Fee"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-gray-500",children:"$"}),e.jsx("input",{type:"text",className:"w-20 rounded-lg border border-gray-200 bg-gray-50 px-2 py-1 text-right",value:C,onChange:s=>{ie(s.target.value)}})]})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:Se(J)})]}),e.jsx("div",{className:"rounded-lg bg-blue-700 p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{d:"M10 2C5.58172 2 2 5.58172 2 10C2 14.4183 5.58172 18 10 18C14.4183 18 18 14.4183 18 10C18 5.58172 14.4183 2 10 2ZM0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10ZM9 5C9 4.44772 9.44772 4 10 4C10.5523 4 11 4.44772 11 5V11C11 11.5523 10.5523 12 10 12C9.44772 12 9 11.5523 9 11V5ZM9 15C9 14.4477 9.44772 14 10 14C10.5523 14 11 14.4477 11 15C11 15.5523 10.5523 16 10 16C9.44772 16 9 15.5523 9 15Z",fill:"currentColor"})}),e.jsx("span",{children:o.length>0?`${o.map(s=>`${s.first_name} ${s.last_name}`).join(", ")} will be notified about this reservation.`:"selected players will be notified about this reservation."})]})}),e.jsx(ke,{loading:K,onClick:Z,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:"Create Lesson"}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:"(You will not be charged yet)"})]})})]})]})]})};let $e=new Ne,fs=new _e;const ps=[{header:"Date",accessor:"date",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"By",accessor:"user",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Sport",accessor:"sport",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Type",accessor:"type",isSorted:!1,isSortedDesc:!1,mappingExist:!1},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{1:"Paid",0:"Reserved",2:"Failed"}},{header:"Action",accessor:""}],hs=({sports:j,club:m,courts:q})=>{const{dispatch:$,state:l}=u.useContext(Pe),{dispatch:P}=u.useContext(Ve),[re,y]=u.useState([]),[h,E]=u.useState(10),[U,C]=u.useState(0),[ie,J]=u.useState(0),[S,O]=u.useState(1),[le,I]=u.useState(!1),[oe,be]=u.useState(!1),[R,K]=u.useState(!1);u.useState([]),u.useState([]);const[M,H]=u.useState(!0);Me();const de=u.useRef(null),[N,A]=u.useState(!1),[_,F]=u.useState(null),[o,Y]=i.useState(!1),[v,me]=i.useState(!1),[ce,ue]=i.useState(!1);i.useState([]);const[a,xe]=i.useState([]),Q=Je({id:ae(),email:ae(),role:ae(),status:ae()}),{register:W,handleSubmit:V,formState:{errors:we}}=Ke({resolver:Ye(Q)});function X(){g(S-1,h)}function ee(){g(S+1,h)}const ge=async()=>{try{$e.setTable("user");const t=await $e.callRestAPI({filter:[`club_id,eq,${m==null?void 0:m.id}`,"role,cs,user"]},"GETALL");xe(t.list||[])}catch(t){console.error("Error fetching players:",t),showToast($,"Error fetching players",3e3,"error")}};async function g(t,r,d={},f=[]){H(!0);try{const x=await fs.getPaginate("reservation",{page:t,limit:r,filter:[...f,`courtmatchup_reservation.club_id,cs,${m==null?void 0:m.id}`,`courtmatchup_booking.reservation_type,cs,${Re.lesson}`],join:["clubs|club_id","booking|booking_id","user|user_id"],size:h}),k=x.list;if(x){const T=k.map(b=>{const w=j.find(D=>D.id===b.sport_id);return{...b,sport:w?w.name:"--"}});H(!1),y(T),E(x.limit),C(x.num_pages),O(x.page),J(x.total),I(x.page>1),be(x.page+1<=x.num_pages)}}catch(x){H(!1),console.log("ERROR",x),Ge(P,x.message)}}const fe=t=>{t.search?g(1,h,{},[`first_name,cs,${t.search}`,`last_name,cs,${t.search}`]):g(1,h)},pe=async t=>{const r=t.target.value;r===""?await g(1,h):await g(1,h,{},[`sport_id,eq,${parseInt(r)}`])},he=async t=>{t.target.value===""?await g(S,h):await g(S,h,{},[`courtmatchup_booking.reservation_type,cs,${t.target.value}`])};u.useEffect(()=>{$({type:"SETPATH",payload:{path:"lessons"}}),m!=null&&m.id&&(g(1,h,{}),ge())},[m==null?void 0:m.id]);const se=t=>{de.current&&!de.current.contains(t.target)&&K(!1)};u.useEffect(()=>(document.addEventListener("mousedown",se),()=>{document.removeEventListener("mousedown",se)}),[]);const Z=t=>{var d,f,x,k,T,b,w,D,z,s,n,c;const r={...t,id:(d=t.booking)==null?void 0:d.id,date:(f=t.booking)==null?void 0:f.date,startTime:(x=t.booking)==null?void 0:x.start_time,endTime:(k=t.booking)==null?void 0:k.end_time,sport_id:(T=t.booking)==null?void 0:T.sport_id,type:(b=t.booking)==null?void 0:b.type,sub_type:(w=t.booking)==null?void 0:w.subtype,reservation_type:(D=t.booking)==null?void 0:D.reservation_type,price:(z=t.booking)==null?void 0:z.price,status:(s=t.booking)==null?void 0:s.status,player_ids:(n=t.booking)==null?void 0:n.player_ids,coach_ids:(c=t.booking)==null?void 0:c.coach_ids};F(r),A(!0)},je={"":t=>e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",onClick:r=>{r.stopPropagation(),Z(t)},children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})}),type:t=>{var r;return e.jsx("span",{className:"capitalize",children:((r=Te.find(d=>{var f;return d.value==((f=t==null?void 0:t.booking)==null?void 0:f.reservation_type)}))==null?void 0:r.label)||"--"})},sport:t=>{var r;return e.jsx("span",{className:"capitalize",children:((r=j.find(d=>{var f;return d.id===((f=t==null?void 0:t.booking)==null?void 0:f.sport_id)}))==null?void 0:r.name)||"--"})},date:t=>{var r,d,f;return e.jsxs(e.Fragment,{children:[qe((r=t==null?void 0:t.booking)==null?void 0:r.date)," "," | "," ",De((d=t==null?void 0:t.booking)==null?void 0:d.start_time)," "," - "," ",De((f=t==null?void 0:t.booking)==null?void 0:f.end_time)]})},players:t=>{var r,d;return e.jsx(e.Fragment,{children:(r=t==null?void 0:t.booking)!=null&&r.player_ids?`${JSON.parse((d=t==null?void 0:t.booking)==null?void 0:d.player_ids).length} players`:"0 players"})},bill:t=>{var r;return e.jsx(e.Fragment,{children:Se((r=t==null?void 0:t.booking)==null?void 0:r.price)})},user:t=>{var r,d;return e.jsx(e.Fragment,{children:`${((r=t==null?void 0:t.user)==null?void 0:r.first_name)||""} ${((d=t==null?void 0:t.user)==null?void 0:d.last_name)||""}`})},status:t=>e.jsxs(e.Fragment,{children:[t.booking.status==ye.SUCCESS&&e.jsx(ss,{}),t.booking.status==ye.PENDING&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(ts,{}),e.jsx(as,{timeLeft:Ue(t==null?void 0:t.reservation_updated_at)})]}),t.booking.status==ye.FAIL&&e.jsx(ns,{})]})};return e.jsxs("div",{className:"h-screen px-2 md:px-8",children:[M&&e.jsx(es,{}),e.jsx("div",{className:"flex flex-col gap-4 py-3",children:e.jsxs("div",{className:"flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between",children:[e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center",children:[e.jsxs("form",{className:"relative flex flex-1 items-center",onSubmit:V(fe),children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(Ee,{className:"text-gray-500"})}),e.jsx("input",{type:"text",className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search users",...W("search")})]}),e.jsxs("div",{className:"flex flex-col gap-2 xs:flex-row xs:gap-4",children:[e.jsx("input",{type:"date",className:"w-full rounded-lg border border-gray-200 text-sm text-gray-500 xs:w-auto"}),e.jsx("input",{type:"time",defaultValue:"00:00",className:"w-full rounded-lg border border-gray-200 text-sm text-gray-500 xs:w-auto"})]})]}),e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:flex-wrap sm:items-center",children:[e.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700 sm:w-auto",defaultValue:"Sport: All",onChange:pe,children:[e.jsx("option",{value:"",children:"Sport: All"}),j==null?void 0:j.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),e.jsxs("select",{className:"w-full rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700 sm:w-auto",defaultValue:"All",onChange:he,children:[e.jsx("option",{value:"",children:"Reservation Type: All"}),Te.map(t=>e.jsx("option",{value:t.value,children:t.label},t.value))]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("button",{onClick:()=>Y(!0),className:"inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[e.jsx("span",{children:"+"}),"Add new"]}),e.jsx(is,{title:"Lesson History",emptyMessage:"No lesson history found"})]})]})]})}),M?e.jsx(We,{}):e.jsx("div",{className:"overflow-x-auto",children:e.jsx(rs,{columns:ps,data:re,loading:M,renderCustomCell:je,rowClassName:"hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer",cellClassName:"whitespace-nowrap px-6 py-4",headerClassName:"px-6 py-4 text-left text-sm font-medium text-gray-500",emptyMessage:"No data available",onClick:t=>Z(t)})}),e.jsx(Qe,{currentPage:S,pageCount:U,pageSize:h,canPreviousPage:le,canNextPage:oe,updatePageSize:t=>{E(t),g(1,t)},previousPage:X,nextPage:ee,gotoPage:t=>g(t,h)}),e.jsx(Ze,{isOpen:N,onClose:()=>A(!1),event:_,users:a,sports:j,club:m,fetchData:g,courts:q}),e.jsx(gs,{isOpen:o,club:m,onClose:()=>Y(!1),sports:j,players:a}),v&&e.jsx(Xe,{courts:q,onClose:()=>me(!1),reservation:_,getData:g,sports:j,setReservation:F}),ce&&e.jsx(ze,{reservation:_,onClose:()=>ue(!1),getData:g,setReservation:F})]})},Hs=hs;export{Hs as L};
