import{j as R}from"./@nivo/heatmap-ba1ecfff.js";import{b as z,r as t}from"./vendor-851db8c1.js";import{S as w}from"./SelectionOptionsCard-0d5c6ddd.js";const C=z.memo(({sports:T,onSelectionChange:E,isChildren:M=!1,initialSport:a=null,initialType:n=null,initialSubType:f=null,userPermissions:y=null})=>{const[l,A]=t.useState(a),[s,h]=t.useState(n),[g,o]=t.useState(f),[$,H]=t.useState([]),[k,O]=t.useState([]),[d,V]=t.useState(!1),[_,j]=t.useState(!1),[c,q]=t.useState(!1),m=t.useRef(a),v=t.useRef(n),I=t.useRef(f),u=z.useMemo(()=>!T||!Array.isArray(T)?[]:!(y!=null&&y.applicable_sports)||!Array.isArray(y.applicable_sports)?T:T.filter(e=>y.applicable_sports.includes(e.id)),[T,y]),i=u==null?void 0:u.find(e=>e.id===l);return t.useEffect(()=>{A(a),h(n),o(f),q(!0)},[a,n,f]),t.useEffect(()=>{if(a!==null||n!==null||f!==null){const e=u==null?void 0:u.find(r=>r.id===a),b=((e==null?void 0:e.sport_types)||[]).filter(r=>r.type&&r.type.trim()!=="").length>0;let N=!1;if(n&&e){const r=e.sport_types.find(x=>x.type===n);N=((r==null?void 0:r.subtype)||[]).filter(x=>x&&x.trim()!=="").length>0}E({sport:a,type:n,subType:f,hasTypes:b,hasSubTypes:N})}},[u]),t.useEffect(()=>{if(i){const e=i.sport_types||[];H(e);const S=e.filter(p=>p.type&&p.type.trim()!=="");V(S.length>0),c&&l!==m.current&&(S.length===0?h(""):h(null),o(null))}else H([]),V(!1),c&&l!==m.current&&(h(null),o(null))},[l,i,c]),t.useEffect(()=>{if(i){if(s!==null){if(!d||s===""){O([]),j(!1),o("");return}const e=i.sport_types.find(b=>b.type===s),p=((e==null?void 0:e.subtype)||[]).filter(b=>b&&b.trim()!=="");O(p),j(p.length>0),c&&s!==v.current&&(p.length===0?o(""):o(null))}}else O([]),j(!1),c&&s!==v.current&&o(null)},[s,i,d,c]),t.useEffect(()=>{if(c){const e=l!==m.current,S=s!==v.current,p=g!==I.current;(e||S||p)&&(E({sport:l,type:s,subType:g,hasTypes:d,hasSubTypes:_}),m.current=l,v.current=s,I.current=g)}},[l,s,g,d,_,c,E]),R.jsxs("div",{className:`h-fit w-full space-y-6 rounded-lg bg-white ${M?"p-0 shadow-none":"p-4 shadow-5"}`,children:[R.jsx(w,{title:"Sports",options:u||[],selectedOption:l,onOptionSelect:A,emptyMessage:"No sports found",optionType:"sport"}),d&&R.jsx(w,{title:"Type",options:$,selectedOption:s,onOptionSelect:h,showPlaceholder:!l,optionType:"type"}),d&&s&&_&&R.jsx(w,{title:"Sub-Type",options:k,selectedOption:g,onOptionSelect:o,showPlaceholder:!s,optionType:"subtype"})]})});C.displayName="SportTypeSelection";const L=C;export{L as S};
