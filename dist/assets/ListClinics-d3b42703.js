import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as c,b as d,f as we}from"./vendor-851db8c1.js";import{am as Ee,d as ye,aj as ke,M as fe,G as be,A as ve,u as Te,R as De,v as ge,a2 as R,e as je,aa as Le,b as Q,T as Fe,c as Ae,D as Be,J as Pe,t as he,W as xe}from"./index-ca7cbd3e.js";import{c as Oe,a as X}from"./yup-54691517.js";import{u as Ne}from"./react-hook-form-687afde5.js";import{o as Me}from"./yup-2824f222.js";import{P as Re}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import $e from"./Skeleton-1e8bf077.js";import{S as M}from"./react-select-c8303602.js";import{H as Ie}from"./HistoryComponent-cf25af7a.js";import{D as He}from"./DataTable-a2248415.js";const ze=({onClose:l,onConfirm:N,eventCounts:_})=>{const[h,a]=c.useState(""),r=()=>{h&&N(h)};return e.jsxs("div",{className:"fixed inset-0 z-[99999] flex items-center justify-center ",children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-2xl rounded-lg bg-white p-6",children:[e.jsxs("div",{className:"flex items-center justify-between border-b pb-4",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Confirm Updates"}),e.jsx("button",{onClick:l,className:"text-gray-500 hover:text-gray-700",children:e.jsx(Ee,{className:"h-6 w-6"})})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"mb-4 text-base",children:"You're changing the Sport / Type / Subtype for this clinic. This affects how the clinic connects to scheduled events; time slots, availability, and visibility in the scheduler."}),e.jsx("p",{className:"mb-2 font-medium",children:"Below are the scheduled events currently tied to this clinic:"}),e.jsxs("div",{className:"mb-6 ml-6",children:[e.jsxs("p",{children:["Total Scheduled Events: ",_.total]}),e.jsxs("p",{children:["Completed Events: ",_.completed]}),e.jsxs("p",{children:["Upcoming Events: ",_.upcoming]}),e.jsxs("p",{children:["Last Event Date: ",_.lastEvent]})]}),e.jsx("p",{className:"mb-4 font-medium",children:"Choose how to handle scheduled events:"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option1",name:"sportChangeOption",value:"1",checked:h==="1",onChange:n=>a(n.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option1",className:"font-medium",children:"Cancel All Future Events"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Delete all upcoming events for this clinic. Past events will remain unchanged."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option2",name:"sportChangeOption",value:"2",checked:h==="2",onChange:n=>a(n.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option2",className:"font-medium",children:"Apply Changes Only to Future Events"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Keep past events intact: Update sport/type/subtype on upcoming events only."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option3",name:"sportChangeOption",value:"3",checked:h==="3",onChange:n=>a(n.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option3",className:"font-medium",children:"Apply Changes to All Events (Past and Future)"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Retroactively apply sport/type/subtype changes to all events connected to this clinic, including completed ones."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option4",name:"sportChangeOption",value:"4",checked:h==="4",onChange:n=>a(n.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option4",className:"font-medium",children:"Clone This Clinic and Keep Existing Schedule"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Keep the current events tied to this clinic, and create a new clinic with your changes that you can schedule separately."})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("input",{type:"radio",id:"option0",name:"sportChangeOption",value:"0",checked:h==="0",onChange:n=>a(n.target.value),className:"mt-1 h-4 w-4"}),e.jsxs("div",{className:"ml-2",children:[e.jsx("label",{htmlFor:"option0",className:"font-medium",children:"Don't Apply Sport/Type/Subtype Changes"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Save all other updates to this clinic, but leave the original sport, type, and subtype unchanged."})]})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t pt-4",children:[e.jsx("button",{onClick:l,className:"rounded-lg border border-gray-300 bg-white px-6 py-2 text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx(ye,{onClick:r,className:"rounded-lg bg-blue-500 px-6 py-2 text-white hover:bg-blue-600",disabled:!h,children:"Confirm and Save Changes"})]})]})]})},k=ke();let Z=new fe;const Ve=({clinic:l,onClose:N,getData:_,isOpen:h})=>{var p,G,J;const a=l||{sport_id:"",type:"",sub_type:"",date:"",end_date:"",start_time:"",end_time:"",name:"",cost_per_head:"",description:"",recurring:0,id:null},{setValue:r,watch:n}=Ne({defaultValues:{sport_id:a.sport_id,type:a.type,sub_type:a.sub_type,date:a.date,end_date:a.end_date,start_time:a.start_time,end_time:a.end_time,name:a.name,cost_per_head:a.cost_per_head,description:a.description,recurring:a.recurring}}),[u,S]=c.useState(!1),[g,L]=c.useState(!1),[ce,$]=c.useState(!1),[y,I]=c.useState([]),[ee,F]=c.useState(!1),[f,H]=c.useState({sport_id:"",type:"",sub_type:""}),[te,A]=c.useState(null),[B,P]=c.useState({total:0,completed:0,upcoming:"No upcoming events",lastEvent:"No events scheduled"}),[z,V]=c.useState(null),[T,U]=c.useState([]),[Y,D]=c.useState([]),[me,K]=c.useState(k),b=n("sport_id"),v=n("type"),C=n("start_time"),{dispatch:w}=c.useContext(be),{dispatch:se}=c.useContext(ve),{sports:x}=Te();c.useEffect(()=>{l&&(r("sport_id",l.sport_id||""),r("type",l.type||""),r("sub_type",l.sub_type||""),r("date",l.date||""),r("end_date",l.end_date||""),r("start_time",l.start_time||""),r("end_time",l.end_time||""),r("name",l.name||""),r("cost_per_head",l.cost_per_head||""),r("description",l.description||""),r("recurring",l.recurring||0),H({sport_id:l.sport_id||"",type:l.type||"",sub_type:l.sub_type||""}))},[l,r]);const W=async()=>{if(!a.id)return[];$(!0);try{Z.setTable("clinic_coaches");const t=await Z.callRestAPI({filter:[`clinic_id,eq,${a.id}`]},"GETALL"),o=await Le(w,se,"coach",t==null?void 0:t.list.map(i=>i.coach_id),"user|user_id");I(o.list)}catch(t){return console.log(t),[]}finally{$(!1)}};c.useEffect(()=>{l!=null&&l.id&&W()},[l==null?void 0:l.id]),c.useEffect(()=>{var t;if(b){const o=x.find(i=>i.id.toString()===b.toString());if(o){const i=((t=o.sport_types)==null?void 0:t.filter(j=>j.type!==""))||[];U(i)}else U([])}else U([])},[b,x]),c.useEffect(()=>{if(v){const t=T.find(o=>o.type===v);if(t){const o=(t.subtype||[]).filter(i=>i!=="");D(o)}else D([])}else D([])},[v,T]);const pe=t=>{if(!t)return k;const o=k.findIndex(i=>i.value===t);return o===-1?k:k.filter((i,j)=>j>o)};c.useEffect(()=>{if(C){const t=pe(C);K(t)}else K(k)},[C]);const ae=async()=>{const t=await W();t&&I(t)},re=async t=>{try{return console.log(`Fetching event counts for clinic ID: ${t}`),{total:12,completed:4,upcoming:"April 3, 2025",lastEvent:"June 19, 2025"}}catch(o){return console.log("Error fetching event counts:",o),{total:0,completed:0,upcoming:"No upcoming events",lastEvent:"No events scheduled"}}},ne=async()=>{if(!a.id){Q(w,"Cannot save: no clinic selected",3e3,"error");return}const t={id:a.id,name:n("name"),cost_per_head:parseFloat(n("cost_per_head")),description:n("description"),sport_id:n("sport_id"),type:n("type"),sub_type:n("sub_type"),date:n("date"),end_date:n("end_date"),start_time:n("start_time"),end_time:n("end_time"),recurring:n("recurring")===1||n("recurring")===!0?1:0};if(t.sport_id!==f.sport_id||t.type!==f.type||t.sub_type!==f.sub_type){S(!0);try{const i=await re(a.id);P(i),F(!0),V(t)}catch(i){Q(w,"Error fetching event information",3e3,"error"),console.log(i)}finally{S(!1)}return}await q(t)},q=async t=>{S(!0);try{te!==null&&(t.sport_change_option=te),Z.setTable("clinics");const o=await Z.callRestAPI(t,"PUT");o!=null&&o.error||(Q(w,"Clinic updated successfully",3e3,"success"),l&&Object.keys(t).forEach(i=>{i!=="id"&&(l[i]=t[i])}),H({sport_id:t.sport_id,type:t.type,sub_type:t.sub_type}),L(!1),A(null),await ae(),_())}catch(o){Q(w,(o==null?void 0:o.message)||"An error occurred",3e3,"error"),console.log(o)}finally{S(!1)}};if(c.useEffect(()=>{g&&(r("name",a.name||""),r("cost_per_head",a.cost_per_head||""),r("description",a.description||""),r("sport_id",a.sport_id||""),r("type",a.type||""),r("sub_type",a.sub_type||""),r("date",a.date||""),r("end_date",a.end_date||""),r("start_time",a.start_time||""),r("end_time",a.end_time||""),r("recurring",a.recurring))},[g,a,r]),!h)return null;const E={control:t=>({...t,borderRadius:"0.5rem",border:"none",backgroundColor:"#f9fafb","&:hover":{border:"none",backgroundColor:"#f3f4f6"},"&:focus-within":{border:"none",boxShadow:"none",backgroundColor:"#f3f4f6"}}),option:(t,o)=>({...t,backgroundColor:o.isSelected?"#3b82f6":o.isFocused?"#f3f4f6":"white",color:o.isSelected?"white":"#374151","&:hover":{backgroundColor:o.isSelected?"#3b82f6":"#f3f4f6"}}),menu:t=>({...t,borderRadius:"0.5rem",boxShadow:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)"}),multiValue:t=>({...t,backgroundColor:"#e5e7eb",borderRadius:"0.375rem"}),multiValueLabel:t=>({...t,color:"#374151",padding:"0.25rem 0.5rem"}),multiValueRemove:t=>({...t,color:"#6b7280",borderRadius:"0 0.375rem 0.375rem 0","&:hover":{backgroundColor:"#d1d5db",color:"#374151"}})};return e.jsxs(e.Fragment,{children:[e.jsx(De,{isOpen:h,onClose:N,title:a.name||"Clinic details",showFooter:!1,children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"rounded-lg border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:a.name||"Clinic Details"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[((p=x.find(t=>t.id.toString()==a.sport_id))==null?void 0:p.name)||"No sport selected",a.type&&` • ${a.type}`,a.sub_type&&` • ${a.sub_type}`]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-lg font-bold text-primaryBlue",children:ge(a.cost_per_head)}),e.jsx("div",{className:"text-sm text-gray-500",children:"per person"})]})]})}),e.jsx("div",{className:"flex justify-end border-b border-gray-200 pb-4",children:g?e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>L(!1),className:"rounded-lg border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 transition-colors hover:border-gray-400 hover:bg-gray-50",children:"Cancel"}),e.jsx(ye,{loading:u,onClick:ne,className:"rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md",children:u?"Saving...":"Save All Changes"})]}):e.jsx("button",{onClick:()=>L(!0),className:"rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),e.jsx("span",{children:"Edit Details"})]})})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Basic Information"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Clinic Name"}),g?e.jsx("input",{type:"text",value:n("name")||"",onChange:t=>r("name",t.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",placeholder:"Enter clinic name"}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.name||"No name provided"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Cost per Person"}),g?e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx("span",{className:"font-medium text-gray-500",children:"$"})}),e.jsx("input",{type:"number",value:n("cost_per_head")||"",onChange:t=>r("cost_per_head",t.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-8 pr-3 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",placeholder:"0.00",min:"0",step:"0.01"})]}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:ge(a.cost_per_head)})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Description"}),g?e.jsx("textarea",{value:n("description")||"",onChange:t=>r("description",t.target.value),className:"w-full resize-none rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",rows:3,placeholder:"Enter clinic description"}):e.jsx("div",{className:"min-h-[80px] rounded-lg bg-gray-50 px-3 py-2 text-gray-900",children:a.description||"No description provided"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Sport Configuration"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Sport"}),g?e.jsx(M,{className:"w-full text-sm",options:x.filter(t=>t.status===1).map(t=>({value:t.id.toString(),label:t.name})),value:{value:b,label:((G=x.find(t=>t.id.toString()==b))==null?void 0:G.name)||"Select sport"},onChange:t=>{r("sport_id",t.value),r("type",""),r("sub_type","")},styles:E}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:((J=x.find(t=>t.id.toString()==a.sport_id))==null?void 0:J.name)||"No sport selected"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Type"}),g?e.jsx(e.Fragment,{children:T.length>0?e.jsx(M,{className:"w-full text-sm",options:T.map(t=>({value:t.type,label:t.type})),value:{value:v,label:v||"Select type"},onChange:t=>{r("type",t.value),r("sub_type","")},styles:E}):e.jsx("div",{className:"rounded-lg bg-gray-100 px-3 py-2 text-sm italic text-gray-500",children:"This sport has no types"})}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.type||"No type selected"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Sub-type"}),g?e.jsx(e.Fragment,{children:Y.length>0?e.jsx(M,{className:"w-full text-sm",options:Y.map(t=>({value:t,label:t})),value:{value:n("sub_type"),label:n("sub_type")||"Select sub-type"},onChange:t=>{r("sub_type",t.value)},styles:E}):e.jsx("div",{className:"rounded-lg bg-gray-100 px-3 py-2 text-sm italic text-gray-500",children:"This type has no sub-types"})}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.sub_type||"No sub-type selected"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Scheduling"})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Start Date"}),g?e.jsx("input",{type:"date",value:n("date")||"",onChange:t=>r("date",t.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.date?new Date(a.date).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):"No start date set"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"End Date"}),g?e.jsx("input",{type:"date",value:n("end_date")||"",onChange:t=>r("end_date",t.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",min:n("date")||void 0}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:a.end_date?new Date(a.end_date).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):"No end date set"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Start Time"}),g?e.jsx(M,{className:"w-full text-sm",options:k,value:{value:n("start_time"),label:R(n("start_time"))||"Select time"},onChange:t=>{r("start_time",t.value)},placeholder:"Select start time",styles:E}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:R(a.start_time)||"Not set"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"End Time"}),g?e.jsx(M,{className:"w-full text-sm",options:me,value:{value:n("end_time"),label:R(n("end_time"))||"Select time"},onChange:t=>{r("end_time",t.value)},placeholder:C?"Select end time":"Select start time first",isDisabled:!C,styles:E}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:R(a.end_time)||"Not set"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Recurring Event"}),g?e.jsxs("select",{value:n("recurring")===1||n("recurring")===!0?"Yes":"No",onChange:t=>r("recurring",t.target.value==="Yes"?1:0),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",children:[e.jsx("option",{value:"No",children:"No"}),e.jsx("option",{value:"Yes",children:"Yes"})]}):e.jsx("div",{className:"rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:`h-2 w-2 rounded-full ${a.recurring===1?"bg-green-400":"bg-gray-400"}`}),e.jsx("span",{children:a.recurring===1?"Yes":"No"})]})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"border-b border-gray-200 pb-2",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h4",{className:"text-sm font-semibold uppercase tracking-wide text-gray-900",children:"Coaches"}),e.jsxs("span",{className:"rounded-full bg-primaryBlue/10 px-2 py-1 text-xs font-semibold text-primaryBlue",children:[y.length," assigned"]})]})}),y.length>0?e.jsx("div",{className:"space-y-3",children:y.map(t=>{var o,i,j,s,m,le;return e.jsxs("div",{className:"flex items-center space-x-3 rounded-lg border border-gray-200 bg-gray-50 p-3 transition-all hover:shadow-sm",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full shadow-sm ring-2 ring-white",children:e.jsx("img",{src:((o=t.user)==null?void 0:o.photo)||t.photo||"/default-avatar.png",alt:`${((i=t.user)==null?void 0:i.first_name)||""} ${((j=t.user)==null?void 0:j.last_name)||""}`,className:"h-full w-full object-cover",onError:oe=>{oe.target.src="/default-avatar.png"}})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"font-semibold text-gray-900",children:[(s=t.user)==null?void 0:s.first_name," ",(m=t.user)==null?void 0:m.last_name]}),((le=t.user)==null?void 0:le.email)&&e.jsx("div",{className:"text-sm text-gray-500",children:t.user.email})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-400"}),e.jsx("span",{className:"text-xs text-gray-500",children:"Active"})]})]},t.id)})}):e.jsxs("div",{className:"rounded-lg border-2 border-dashed border-gray-200 p-6 text-center",children:[e.jsx("div",{className:"mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100",children:e.jsx("svg",{className:"h-6 w-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"})})}),e.jsx("p",{className:"font-medium text-gray-500",children:"No coaches assigned"}),e.jsx("p",{className:"mt-1 text-sm text-gray-400",children:"Coaches will appear here when assigned to this clinic"})]})]}),ce&&e.jsx(je,{})]})})," ",ee&&e.jsx(ze,{onClose:()=>{r("sport_id",f.sport_id),r("type",f.type),r("sub_type",f.sub_type),A(null),V(null),F(!1)},onConfirm:async t=>{A(parseInt(t)),F(!1),z&&await q(z),V(null)},eventCounts:B})]})};let de=new fe,Ue=new Fe;const Ye=[{header:"Clinic ID",accessor:"id"},{header:"Name",accessor:"name"},{header:"Max participants",accessor:"max_participants"},{header:"Date",accessor:"date"},{header:"End date",accessor:"end_date"},{header:"Time",accessor:"start_time"},{header:"Sport",accessor:"sport"},{header:"Fee",accessor:"cost_per_head"},{header:"",accessor:"actions"}],Ke=({club:l,sports:N,courts:_})=>{const{dispatch:h}=d.useContext(be),{dispatch:a}=d.useContext(ve),[r,n]=d.useState([]),[u,S]=d.useState(10),[g,L]=d.useState(0),[ce,$]=d.useState(0),[y,I]=d.useState(0),[ee,F]=d.useState(!1),[f,H]=d.useState(!1),[te,A]=d.useState(!1);d.useState([]),d.useState([]);const[B,P]=d.useState(!0),[z,V]=d.useState(!1),[T,U]=d.useState(!1),Y=we(),D=d.useRef(null),[me,K]=d.useState(!1),[b,v]=d.useState(null),[C,w]=d.useState(!1),[se,x]=d.useState(!1),[W,pe]=d.useState(!1);d.useState([]);const ae=Oe({id:X(),email:X(),role:X(),status:X()});Ne({resolver:Me(ae)});const re=s=>{s===""?p(1,u):p(1,u,{},[`courtmatchup_clinics.name,cs,${s}`])},ne=s=>{console.log("date search",s),s===""?p(1,u):p(1,u,{},[`date,cs,${s}`])};function q(){p(y-1,u)}function E(){p(y+1,u)}async function p(s,m,le={},oe=[]){P(!(T||z));try{de.setTable("clinics");const O=await Ue.getPaginate("clinics",{page:s,limit:m,filter:[...oe,`courtmatchup_clinics.club_id,eq,${l==null?void 0:l.id}`],join:["sports|sport_id"]});O&&P(!1);const{list:_e,total:Se,limit:Ce,num_pages:ue,page:ie}=O;n(_e),S(Ce),L(ue),I(ie),$(Se),F(ie>1),H(ie+1<=ue)}catch(O){P(!1),console.log("ERROR",O),he(a,O.message)}}const G=s=>{s.target.value===""?p(1,u):p(1,u,{},[`status,cs,${s.target.value}`])},J=s=>{const m=s.target.value;m===""?p(1,u):p(1,u,{},[`sport_id,eq,${m}`])};d.useEffect(()=>{if(h({type:"SETPATH",payload:{path:"program-clinics"}}),!(l!=null&&l.id))return;const m=setTimeout(async()=>{await p(1,u)},700);return()=>{clearTimeout(m)}},[l]);const t=s=>{D.current&&!D.current.contains(s.target)&&A(!1)};d.useEffect(()=>(document.addEventListener("mousedown",t),()=>{document.removeEventListener("mousedown",t)}),[]);const o=async s=>{x(!0);try{de.setTable("clinics"),await de.callRestAPI({id:s},"DELETE"),p(y,u)}catch(m){console.error("Error deleting clinic:",m),he(a,m.message)}finally{x(!1)}},i=s=>{const m={...s,id:s==null?void 0:s.id,date:s==null?void 0:s.date,startTime:s==null?void 0:s.start_time,endTime:s==null?void 0:s.end_time,sport_id:s==null?void 0:s.sport_id,type:s==null?void 0:s.type,sub_type:s==null?void 0:s.sub_type,reservation_type:3,price:s==null?void 0:s.price,status:s==null?void 0:s.status,player_ids:s==null?void 0:s.player_ids,coach_ids:s==null?void 0:s.coach_ids};v(m),K(!0)},j={actions:s=>e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",onClick:m=>{m.stopPropagation(),i(s)},children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})}),status:s=>e.jsx("span",{className:`rounded-lg px-3 py-1 text-sm ${s.status===1?"bg-[#D1FAE5] text-[#065F46]":"bg-[#F4F4F4] text-[#393939]"}`,children:s.status===1?"Active":"Inactive"}),start_time:s=>R(s==null?void 0:s.start_time),date:s=>xe(s==null?void 0:s.date),end_date:s=>xe(s==null?void 0:s.end_date),players:s=>s!=null&&s.player_ids?`${JSON.parse(s==null?void 0:s.player_ids).length} players`:"0 players",sport:s=>{var m;return(m=s==null?void 0:s.sports)==null?void 0:m.name}};return e.jsxs("div",{className:"h-screen px-2 md:px-8",children:[se||B&&e.jsx(je,{}),e.jsx("div",{className:"flex flex-col gap-4 py-3",children:e.jsxs("div",{className:"flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between",children:[e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center",children:[e.jsxs("div",{className:"relative flex w-full max-w-xs flex-1 items-center",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(Ae,{className:"text-gray-500"})}),e.jsx("input",{type:"text",className:"block w-full rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search clinics",onChange:s=>re(s.target.value)})]}),e.jsx("input",{type:"date",className:"w-full rounded-md border border-gray-200 text-sm text-gray-500 sm:w-auto",onChange:s=>ne(s.target.value)})]}),e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:flex-wrap sm:items-center",children:[e.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700 sm:w-auto",defaultValue:"All",onChange:J,children:[e.jsx("option",{value:"",children:"Sport: All"}),N==null?void 0:N.map(s=>e.jsx("option",{value:s.id,children:s.name},s.id))]}),e.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700 sm:w-auto",defaultValue:"All",onChange:G,children:[e.jsx("option",{value:"",children:"Status: All"}),e.jsx("option",{value:"0",children:"Inactive"}),e.jsx("option",{value:"1",children:"Active"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("button",{onClick:()=>Y("/club/program-clinics/add"),className:"inline-flex items-center gap-2 rounded-md bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[e.jsx("span",{children:"+"}),"Add new"]}),e.jsx(Ie,{title:"Clinic History",emptyMessage:"No clinic history found",activityType:Be.clinic})]})]})]})}),B?e.jsx($e,{}):e.jsx(He,{columns:Ye,data:r,loading:B,renderCustomCell:j,rowClassName:"hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer",emptyMessage:"No clinics available",loadingMessage:"Loading clinics...",onClick:s=>i(s)}),e.jsx(Re,{currentPage:y,pageCount:g,pageSize:u,canPreviousPage:ee,canNextPage:f,updatePageSize:s=>{S(s),p(1,s)},previousPage:q,nextPage:E,gotoPage:s=>p(s,u)}),e.jsx(Pe,{isOpen:C,onClose:()=>w(!1),onDelete:o,loading:W,title:"Delete",message:"Are you sure you want to delete this clinic?"}),e.jsx(Ve,{getData:p,onClose:()=>v(null),clinic:b,isOpen:b!==null})]})},nt=Ke;export{nt as L};
