import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{j as _,f as E,r as i}from"./vendor-851db8c1.js";import{d as h,M as P}from"./index-ca7cbd3e.js";import{a as j,r as A,s as y,m as f}from"./index.esm-09a3a6b8.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let m=new P;function de(){const{id:d}=_(),o=E(),[s,N]=i.useState(null),[w,p]=i.useState(!0),[u,a]=i.useState(!1),[x,n]=i.useState(null),[c,r]=i.useState(null),[l,v]=i.useState(!1),b=async()=>{p(!0),r(null);try{const t=await m.callRawAPI("/v3/api/custom/courtmatchup/user/groups/pending-invites",{},"GET");if(t.invites){const g=t.invites.find(S=>S.group_id===parseInt(d));g?N(g):r("Invite not found or may have already been processed.")}else r("No pending invites found.")}catch(t){r("Error fetching invite details: "+t.message)}finally{p(!1)}},I=async()=>{if(s){a(!0),n(null),r(null);try{const t={invite_id:s.invite_id,status:"accepted"};await m.callRawAPI("/v3/api/custom/courtmatchup/user/groups/invite-response",t,"POST"),n("Invite accepted successfully! You are now part of the group."),v(!0),setTimeout(()=>{o("/user/my-groups")},2e3)}catch(t){r("Error accepting invite: "+t.message)}finally{a(!1)}}},G=async()=>{if(s){a(!0),n(null),r(null);try{const t={invite_id:s.invite_id,status:"rejected"};await m.callRawAPI("/v3/api/custom/courtmatchup/user/groups/invite-response",t,"POST"),n("Invite rejected successfully."),v(!0),setTimeout(()=>{o("/user/my-groups")},2e3)}catch(t){r("Error rejecting invite: "+t.message)}finally{a(!1)}}};return i.useEffect(()=>{b()},[d]),w?e.jsx("div",{className:"flex min-h-screen items-center justify-center bg-gray-50",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-primaryBlue border-t-transparent"}),e.jsx("p",{className:"text-gray-600",children:"Loading invite details..."})]})}):e.jsx("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:e.jsxs("div",{className:"w-full max-w-md",children:[e.jsxs("div",{className:"rounded-2xl bg-white p-6 shadow-lg",children:[e.jsxs("div",{className:"mb-6 text-center",children:[e.jsx("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primaryBlue/10",children:e.jsx(j,{className:"h-8 w-8 text-primaryBlue"})}),e.jsx("h1",{className:"text-2xl font-semibold text-gray-900",children:"Group Invitation"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"You've been invited to join a group"})]}),s&&!l&&e.jsxs("div",{className:"mb-6 space-y-4",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(j,{className:"h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900",children:s.group_name||"Group"}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Group ID: ",s.group_id]})]})]})}),s.inviter_name&&e.jsx("div",{className:"rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(A,{className:"h-5 w-5 text-gray-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Invited by"}),e.jsx("p",{className:"font-medium text-gray-900",children:s.inviter_name})]})]})})]}),s&&!l&&e.jsxs("div",{className:"space-y-3",children:[e.jsx(h,{onClick:I,loading:u,className:"w-full rounded-xl bg-primaryGreen px-6 py-3 text-white hover:bg-primaryGreen/90",children:e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx(y,{className:"mr-2 h-5 w-5"}),"Accept Invitation"]})}),e.jsx(h,{onClick:G,loading:u,className:"w-full rounded-xl border border-gray-300 bg-white px-6 py-3 text-gray-700 hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx(f,{className:"mr-2 h-5 w-5"}),"Decline Invitation"]})})]}),x&&e.jsx("div",{className:"mt-4 rounded-lg bg-green-50 p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(y,{className:"mr-2 h-5 w-5 text-green-500"}),e.jsx("p",{className:"text-green-700",children:x})]})}),c&&e.jsx("div",{className:"mt-4 rounded-lg bg-red-50 p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(f,{className:"mr-2 h-5 w-5 text-red-500"}),e.jsx("p",{className:"text-red-700",children:c})]})}),(l||c)&&e.jsx("div",{className:"mt-6 text-center",children:e.jsx("button",{onClick:()=>o("/user/my-groups"),className:"font-medium text-primaryBlue hover:text-primaryBlue/80",children:"Go to My Groups"})})]}),!l&&!c&&s&&e.jsx("div",{className:"mt-4 text-center",children:e.jsx("p",{className:"text-sm text-gray-500",children:"This invitation will allow you to join the group and participate in group activities."})})]})})}export{de as default};
