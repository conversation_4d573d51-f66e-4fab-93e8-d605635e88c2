import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{f as Y,b as r,L as J}from"./vendor-851db8c1.js";import{M as Q,G as X,A as ee,D as M,t as se,d as te,E as ae,H as re,b as S}from"./index-ca7cbd3e.js";import{P as le}from"./index-eb1bc208.js";import{_ as P}from"./lodash-91d5d207.js";import ne from"./Skeleton-1e8bf077.js";import{H as ie}from"./HistoryComponent-cf25af7a.js";import{D as oe}from"./DataTable-a2248415.js";import{F as de}from"./FormattedPhoneNumber-40dd7178.js";let l=new Q;const ce=[{header:"Name",accessor:"name"},{header:"Email",accessor:"email"},{header:"Phone number",accessor:"phone_number"},{header:"Membership status",accessor:"slug"},{header:"NTRP",accessor:"ntrp"},{header:"",accessor:"actions"}],me=({club:n})=>{const k=Y(),{dispatch:E}=r.useContext(X),{dispatch:h}=r.useContext(ee),[H,V]=r.useState([]),[d,j]=r.useState(10),[v,Z]=r.useState(0),[x,$]=r.useState(0),[D,T]=r.useState(!1),[A,F]=r.useState(!1),[t,u]=r.useState({firstName:"",lastName:"",email:"",phone:"",ageGroup:"",status:"",ntrp:"",membership:""}),[N,f]=r.useState(!0),[R,G]=r.useState(!1),C=r.useRef(null),[I,w]=r.useState(!1),[g,y]=r.useState(null),U=localStorage.getItem("user");function z(){c(x-1,d)}function B(){c(x+1,d)}async function c(s,a,p={},m=[]){f(!R),console.log("filters",m);try{l.setTable("profile");const o=await l.callRestAPI({payload:{...p},page:s,limit:a,filter:[...m,"role,cs,user",`club_id,eq,${n==null?void 0:n.id}`],join:["user|user_id"]},"PAGINATE");o&&f(!1);const{list:i,limit:W,num_pages:_,page:b}=o;V(i),j(W),Z(_),$(b),T(b>1),F(b+1<=_)}catch(o){f(!1),console.log("ERROR",o),se(h,o.message)}}const O=()=>{const s=[];t.firstName&&s.push(`${l._project_id}_user.first_name,cs,${t.firstName}`),t.lastName&&s.push(`${l._project_id}_user.last_name,cs,${t.lastName}`),t.email&&s.push(`${l._project_id}_user.email,cs,${t.email}`),t.phone&&s.push(`${l._project_id}_user.phone,cs,${t.phone}`),t.ageGroup&&t.ageGroup!==""&&s.push(`${l._project_id}_age_group,cs,${t.ageGroup}`),t.status!==""&&s.push(`${l._project_id}_status,eq,${t.status}`),t.ntrp&&s.push(`ntrp,cs,${t.ntrp}`),t.membership&&s.push(`slug,cs,${t.membership}`),c(1,d,{},s)};r.useEffect(()=>{E({type:"SETPATH",payload:{path:"users"}}),n!=null&&n.id&&c(1,d)},[n==null?void 0:n.id]);const L=s=>{C.current&&!C.current.contains(s.target)&&setOpenFilter(!1)};r.useEffect(()=>(document.addEventListener("mousedown",L),()=>{document.removeEventListener("mousedown",L)}),[]);const q={name:s=>{var a,p,m,o,i;return e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:((a=s.user)==null?void 0:a.photo)||"/default-avatar.png",alt:`${(p=s.user)==null?void 0:p.first_name} ${(m=s.user)==null?void 0:m.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsxs("span",{className:"font-medium text-gray-900",children:[P.truncate((o=s.user)==null?void 0:o.first_name,{length:15})," ",P.truncate((i=s.user)==null?void 0:i.last_name,{length:15})]})]})},slug:s=>e.jsx("span",{className:"text-gray-600",children:s.slug||"Member"}),ntrp:s=>e.jsx("span",{className:"text-gray-600",children:s.ntrp}),phone_number:s=>{var a;return e.jsx(de,{phoneNumber:(a=s.user)==null?void 0:a.phone,className:"text-gray-600"})},email:s=>{var a;return e.jsx("span",{className:"text-gray-600",children:(a=s.user)==null?void 0:a.email})},actions:s=>e.jsxs("div",{className:"flex items-center justify-end gap-3",children:[e.jsx(J,{to:`/club/view-user/${s.id}`,className:"rounded-full p-2 hover:bg-gray-100",onClick:a=>a.stopPropagation(),children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{onClick:a=>{a.stopPropagation(),y(s),w(!0)},className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.74071 16.928L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49738 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49738 4.97796Z",fill:"#868C98"})})})]})},K=({isOpen:s,onClose:a})=>{const[p,m]=r.useState(!1),o=async()=>{try{m(!0),l.setTable("profile");const i=await l.callRestAPI({id:Number(g.id)},"DELETE");l.setTable("user"),await l.callRestAPI({id:Number(g.user_id)},"DELETE"),await ae(l,{user_id:U,activity_type:M.user_management,action_type:re.DELETE,data:g,club_id:n==null?void 0:n.id,description:"Deleted user"}),i.error||(S(h,"User deleted successfully",3e3,"success"),a(),y(null),c(x,d))}catch(i){S(h,i==null?void 0:i.message,3e3,"error"),console.log(i)}finally{m(!1)}};return e.jsxs("div",{className:`fixed inset-0 z-50 flex items-center justify-center ${s?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-3xl bg-white p-6",children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Delete user"}),e.jsx("p",{className:"mb-6",children:"Are you sure you want to delete this user?"}),e.jsxs("div",{className:"flex justify-end gap-3 border-t pt-4",children:[e.jsx("button",{onClick:a,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),e.jsx(te,{onClick:()=>{o()},className:"rounded-lg bg-red-600 px-4 py-2 text-white hover:bg-red-700",loading:p,children:"Yes, delete"})]})]})]})};return e.jsxs("div",{className:"h-full px-8",children:[e.jsx("div",{className:"flex flex-col flex-wrap justify-between gap-4 py-3 md:flex-row md:items-center",children:e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"User Filters"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{onClick:()=>G(!0),className:"inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[e.jsx("span",{children:"+"}),"Add new"]}),e.jsx(ie,{title:"User History",emptyMessage:"No user history found",activityType:M.user_management})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"First Name"}),e.jsx("input",{type:"text",placeholder:"Search by first name",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500",value:t.firstName,onChange:s=>{u({...t,firstName:s.target.value.trim()})}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"Last Name"}),e.jsx("input",{type:"text",placeholder:"Search by last name",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500",value:t.lastName,onChange:s=>{u({...t,lastName:s.target.value.trim()})}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"Email"}),e.jsx("input",{type:"text",placeholder:"Search by email",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500",value:t.email,onChange:s=>{u({...t,email:s.target.value.trim()})}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"Phone"}),e.jsx("input",{type:"text",placeholder:"Search by phone",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500",value:t.phone,onChange:s=>{u({...t,phone:s.target.value.trim()})}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"Status"}),e.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700",value:t.status,onChange:s=>{u({...t,status:s.target.value})},children:[e.jsx("option",{value:"",children:"All Statuses"}),e.jsx("option",{value:"0",children:"Inactive"}),e.jsx("option",{value:"1",children:"Active"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"NTRP"}),e.jsx("input",{type:"text",placeholder:"Search by NTRP",className:"w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500",value:t.ntrp,onChange:s=>{u({...t,ntrp:s.target.value.trim()})}})]}),e.jsxs("div",{className:"col-span-full mt-6 flex justify-end gap-3",children:[e.jsx("button",{onClick:()=>{u({firstName:"",lastName:"",email:"",phone:"",ageGroup:"",status:"",ntrp:"",membership:""}),c(1,d)},className:"rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Clear Filters"}),e.jsx("button",{onClick:()=>O(),className:"rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Apply Filters"})]})]})]})}),N?e.jsx(ne,{}):e.jsx(e.Fragment,{children:e.jsx(oe,{columns:ce,data:H,loading:N,renderCustomCell:q,rowClassName:"hover:bg-gray-50 bg-gray-100 px-4 py-3 cursor-pointer",emptyMessage:"No users available",loadingMessage:"Loading users...",onClick:s=>k(`/club/view-user/${s.id}`)})}),v>0&&e.jsx(le,{currentPage:x,pageCount:v,pageSize:d,canPreviousPage:D,canNextPage:A,updatePageSize:s=>{j(s),c(1,s)},previousPage:z,nextPage:B,gotoPage:s=>c(s,d)}),e.jsx(K,{isOpen:I,onClose:()=>{w(!1),y(null)},selectedUser:g})]})},ve=me;export{ve as L};
