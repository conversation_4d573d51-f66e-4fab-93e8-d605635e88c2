import{j as a}from"./@nivo/heatmap-ba1ecfff.js";import{b as ee}from"./index.esm-51ae62c8.js";import{r as b,f as ae,b as se}from"./vendor-851db8c1.js";import{u as P,d as te,G as le,e as ne,v as ie,R as ce,M as oe,b as I,D as re,H as de}from"./index-ca7cbd3e.js";import{b as c}from"./@headlessui/react-a5400090.js";import{C as ue}from"./ChevronRightIcon-efb4c46c.js";function be({onSubmit:$,onClose:o,initialData:r,mode:x}){var S,y;const{sports:d}=P(),[p,u]=b.useState({name:x==="create",price:x==="create",features:new Set});console.log("currentMode",x);const[m,v]=b.useState(!1),C=r?{...r,advance_booking_days:r.advance_booking_days||{court:10,lesson:10,clinic:10,buddy:10},advance_booking_enabled:r.advance_booking_enabled||{court:!0,lesson:!0,clinic:!0,buddy:!0},applicable_sports:r.applicable_sports||[]}:{plan_id:null,plan_name:"",price:0,allow_clinic:!1,allow_buddy:!1,allow_coach:!1,allow_groups:!1,allow_court:!1,features:[],advance_booking_days:{court:10,lesson:10,clinic:10,buddy:10},advance_booking_enabled:{court:!0,lesson:!0,clinic:!0,buddy:!0},applicable_sports:[]},[t,n]=b.useState(C),B=async()=>{v(!0);const e={...t,advance_booking_enabled:{court:!!t.advance_booking_enabled.court,lesson:!!t.advance_booking_enabled.lesson,clinic:!!t.advance_booking_enabled.clinic,buddy:!!t.advance_booking_enabled.buddy}};console.log("Submitting plan data:",e),await $(e,x),v(!1)},R=(e,s)=>{n(l=>({...l,features:l.features.map(i=>i.id===e?{...i,text:s}:i)})),u(l=>{const i=new Set(l.features);return i.delete(e),{...l,features:i}})},j=e=>{n(s=>({...s,features:s.features.filter(l=>l.id!==e)}))},N=()=>{const e=Math.max(...t.features.map(s=>s.id),0)+1;n(s=>({...s,features:[...s.features,{id:e,text:""}]})),u(s=>({...s,features:new Set([...s.features,e])}))},M={allow_court:"Court booking",allow_clinic:"Clinics",allow_coach:"Lesson",allow_buddy:"Find a Buddy",allow_groups:"My Groups"},_=(e,s)=>{n(l=>({...l,[e]:s})),u(l=>({...l,[e]:!1}))},F=e=>{n(s=>({...s,[e]:!s[e]}))},T=e=>{n(s=>{const l=s.applicable_sports||[];return l.includes(e)?{...s,applicable_sports:l.filter(f=>f!==e)}:{...s,applicable_sports:[...l,e]}})},g=()=>{const s=((d==null?void 0:d.filter(l=>l.status===1))||[]).map(l=>l.id);n(l=>({...l,applicable_sports:s}))},A=()=>{n(e=>({...e,applicable_sports:[]}))};return a.jsxs("div",{className:"flex h-full flex-col gap-4",children:[a.jsxs("div",{className:"flex-1 space-y-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"text-sm text-gray-600",children:"Plan name"}),a.jsx("button",{className:"text-sm text-blue-600",onClick:()=>u(e=>({...e,name:!0})),children:"Edit"})]}),p.name?a.jsx("input",{type:"text",value:t.plan_name,onChange:e=>n(s=>({...s,plan_name:e.target.value})),onBlur:()=>_("plan_name",t.plan_name),className:"w-full rounded-md border border-gray-300 px-3 py-2",autoFocus:!0}):a.jsx("div",{children:t==null?void 0:t.plan_name})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"text-sm text-gray-600",children:"Price"}),a.jsx("button",{className:"text-sm text-blue-600",onClick:()=>u(e=>({...e,price:!0})),children:"Edit"})]}),p.price?a.jsx("input",{type:"number",value:t.price,onChange:e=>n(s=>({...s,price:parseFloat(e.target.value)})),onBlur:()=>_("price",t.price),className:"w-full rounded-md border border-gray-300 px-3 py-2",step:"0.01",autoFocus:!0}):a.jsxs("div",{children:["$",(S=t.price)==null?void 0:S.toFixed(2)]})]}),a.jsxs("div",{className:"space-y-4 pt-4",children:[a.jsx("div",{className:"text-sm text-gray-600",children:"Module"}),a.jsx("div",{className:"space-y-4",children:Object.entries(M).map(([e,s])=>a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("div",{children:s}),a.jsx(c,{checked:t[e],onChange:()=>F(e),className:`${t[e]?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:a.jsx("span",{className:`${t[e]?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]},e))})]}),a.jsxs("div",{className:"space-y-4 pt-4",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"text-sm text-gray-600",children:"Applicable Sports"}),a.jsxs("div",{className:"flex gap-2",children:[a.jsx("button",{type:"button",onClick:g,className:"text-xs text-blue-600 hover:text-blue-800",children:"Select All"}),a.jsx("span",{className:"text-xs text-gray-400",children:"|"}),a.jsx("button",{type:"button",onClick:A,className:"text-xs text-blue-600 hover:text-blue-800",children:"Clear All"})]})]}),a.jsx("div",{className:"space-y-3",children:(d==null?void 0:d.filter(e=>e.status===1).length)>0?d.filter(e=>e.status===1).map(e=>{var s;return a.jsx("div",{className:"flex items-center justify-between",children:a.jsxs("div",{className:"flex items-center gap-3",children:[a.jsx("input",{type:"checkbox",id:`sport-${e.id}`,checked:((s=t.applicable_sports)==null?void 0:s.includes(e.id))||!1,onChange:()=>T(e.id),className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),a.jsx("label",{htmlFor:`sport-${e.id}`,className:"cursor-pointer text-sm font-medium text-gray-700",children:e.name})]})},e.id)}):a.jsx("div",{className:"text-sm italic text-gray-500",children:"No active sports available. Please add sports in your club settings first."})}),((y=t.applicable_sports)==null?void 0:y.length)===0&&a.jsx("div",{className:"rounded-md bg-amber-50 p-2 text-xs text-amber-600",children:"⚠️ No sports selected. This membership will not apply to any specific sports."})]}),a.jsxs("div",{className:"space-y-4 pt-4",children:[a.jsx("div",{className:"text-sm text-gray-600",children:"Advance Booking Days"}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"space-y-2",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("div",{children:"Court Reservation"}),a.jsx(c,{checked:t.advance_booking_enabled.court,onChange:()=>{const e=!t.advance_booking_enabled.court;console.log("Toggling court booking enabled:",e),n(s=>({...s,advance_booking_enabled:{...s.advance_booking_enabled,court:e}}))},className:`${t.advance_booking_enabled.court?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:a.jsx("span",{className:`${t.advance_booking_enabled.court?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),t.advance_booking_enabled.court&&a.jsxs("div",{className:"flex items-center justify-between pl-4",children:[a.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),a.jsx("input",{type:"number",min:"1",max:"365",value:t.advance_booking_days.court,onChange:e=>n(s=>({...s,advance_booking_days:{...s.advance_booking_days,court:parseInt(e.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("div",{children:"Lesson Booking"}),a.jsx(c,{checked:t.advance_booking_enabled.lesson,onChange:()=>{const e=!t.advance_booking_enabled.lesson;console.log("Toggling lesson booking enabled:",e),n(s=>({...s,advance_booking_enabled:{...s.advance_booking_enabled,lesson:e}}))},className:`${t.advance_booking_enabled.lesson?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:a.jsx("span",{className:`${t.advance_booking_enabled.lesson?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),t.advance_booking_enabled.lesson&&a.jsxs("div",{className:"flex items-center justify-between pl-4",children:[a.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),a.jsx("input",{type:"number",min:"1",max:"365",value:t.advance_booking_days.lesson,onChange:e=>n(s=>({...s,advance_booking_days:{...s.advance_booking_days,lesson:parseInt(e.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("div",{children:"Clinic/Program Booking"}),a.jsx(c,{checked:t.advance_booking_enabled.clinic,onChange:()=>{const e=!t.advance_booking_enabled.clinic;console.log("Toggling clinic booking enabled:",e),n(s=>({...s,advance_booking_enabled:{...s.advance_booking_enabled,clinic:e}}))},className:`${t.advance_booking_enabled.clinic?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:a.jsx("span",{className:`${t.advance_booking_enabled.clinic?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),t.advance_booking_enabled.clinic&&a.jsxs("div",{className:"flex items-center justify-between pl-4",children:[a.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),a.jsx("input",{type:"number",min:"1",max:"365",value:t.advance_booking_days.clinic,onChange:e=>n(s=>({...s,advance_booking_days:{...s.advance_booking_days,clinic:parseInt(e.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]}),a.jsxs("div",{className:"space-y-2",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("div",{children:"Find-a-Buddy Booking"}),a.jsx(c,{checked:t.advance_booking_enabled.buddy,onChange:()=>{const e=!t.advance_booking_enabled.buddy;console.log("Toggling buddy booking enabled:",e),n(s=>({...s,advance_booking_enabled:{...s.advance_booking_enabled,buddy:e}}))},className:`${t.advance_booking_enabled.buddy?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:a.jsx("span",{className:`${t.advance_booking_enabled.buddy?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),t.advance_booking_enabled.buddy&&a.jsxs("div",{className:"flex items-center justify-between pl-4",children:[a.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),a.jsx("input",{type:"number",min:"1",max:"365",value:t.advance_booking_days.buddy,onChange:e=>n(s=>({...s,advance_booking_days:{...s.advance_booking_days,buddy:parseInt(e.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]})]})]}),a.jsxs("div",{className:"space-y-4 pt-4",children:[a.jsx("div",{className:"text-sm text-gray-600",children:"Plan features"}),a.jsxs("div",{className:"space-y-4",children:[t.features.map(e=>a.jsxs("div",{className:"space-y-2",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"text-sm text-gray-600",children:["Feature ",e.id]}),a.jsxs("div",{className:"space-x-4",children:[a.jsx("button",{className:"text-sm text-red-600",onClick:()=>j(e.id),children:"Delete"}),a.jsx("button",{className:"text-sm text-blue-600",onClick:()=>u(s=>({...s,features:new Set([...s.features,e.id])})),children:"Edit"})]})]}),p.features.has(e.id)?a.jsx("input",{type:"text",value:e.text,onChange:s=>n(l=>({...l,features:l.features.map(i=>i.id===e.id?{...i,text:s.target.value}:i)})),onBlur:()=>R(e.id,e.text),className:"w-full rounded-md border border-gray-300 px-3 py-2",autoFocus:!0}):a.jsx("div",{children:e.text})]},e.id)),a.jsx("button",{className:"text-sm text-blue-600",onClick:N,children:"+ Add feature"})]})]})]}),a.jsxs("div",{className:"flex  flex-shrink-0 justify-end gap-4 border-t border-gray-200 px-4 py-4",children:[a.jsx("button",{type:"button",className:"flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:o,children:"Cancel"}),a.jsx(te,{loading:m,type:"submit",className:"flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",onClick:B,children:x=="edit"?"Save changes":"Create plan"})]})]})}let w=new oe;function ye({fetchProfileSettings:$,membershipPlans:o,profileSettings:r,role:x}){const[d,p]=b.useState(null),[u,m]=b.useState(!1),[v,C]=b.useState(""),[t,n]=b.useState(!1);ae();const[B,R]=b.useState(!1),{club:j,sports:N}=P(),M=localStorage.getItem("user"),{dispatch:_,state:F}=se.useContext(le),T=e=>{if(!e||e.length===0)return"All Sports";if(!N||N.length===0)return"Loading...";const s=e.map(l=>{var i;return(i=N.find(f=>f.id===l))==null?void 0:i.name}).filter(Boolean);return s.length===0?"No Sports":s.length>2?`${s.slice(0,2).join(", ")} +${s.length-2} more`:s.join(", ")},g=e=>{e.preventDefault(),n(!0),I(_,"Please use the edit button to modify plan settings",3e3,"warning"),setTimeout(()=>n(!1),820)},A=e=>{p(e),m(!0)},S=async(e,s)=>{var l,i,f,E,L,O,q,G,V,U,X;try{let h;if(s==="edit")h={membership_settings:o.map(k=>{var D,z,H,K,Q,W,Y,Z;return k.plan_id===e.plan_id?{plan_id:e.plan_id,plan_name:e.plan_name,price:e.price,allow_clinic:e.allow_clinic,allow_buddy:e.allow_buddy,allow_coach:e.allow_coach,allow_groups:e.allow_groups,allow_court:e.allow_court,features:e.features,applicable_sports:e.applicable_sports||[],advance_booking_days:{court:((D=e.advance_booking_days)==null?void 0:D.court)||10,lesson:((z=e.advance_booking_days)==null?void 0:z.lesson)||10,clinic:((H=e.advance_booking_days)==null?void 0:H.clinic)||10,buddy:((K=e.advance_booking_days)==null?void 0:K.buddy)||10},advance_booking_enabled:{court:((Q=e.advance_booking_enabled)==null?void 0:Q.court)!==!1,lesson:((W=e.advance_booking_enabled)==null?void 0:W.lesson)!==!1,clinic:((Y=e.advance_booking_enabled)==null?void 0:Y.clinic)!==!1,buddy:((Z=e.advance_booking_enabled)==null?void 0:Z.buddy)!==!1}}:k})};else{const k=await w.callRawAPI("/v3/api/custom/courtmatchup/stripe/product",{name:e.plan_name,description:e.plan_name,club_id:(i=(l=F.clubProfile)==null?void 0:l.club)==null?void 0:i.id},"POST");console.log("stripeProductResponse",k);const D=await w.callRawAPI("/v3/api/custom/courtmatchup/stripe/price",{product_id:k.model,name:e.plan_name,amount:e.price,type:"recurring",interval:"month",interval_count:1,trial_days:0,usage_type:"licenced",usage_limit:0},"POST");h={membership_settings:[...o,{plan_id:o.length+1,plan_name:e.plan_name,price:e.price,allow_clinic:e.allow_clinic,allow_buddy:e.allow_buddy,allow_coach:e.allow_coach,allow_groups:e.allow_groups,allow_court:e.allow_court,features:e.features,applicable_sports:e.applicable_sports||[],advance_booking_days:{court:((f=e.advance_booking_days)==null?void 0:f.court)||10,lesson:((E=e.advance_booking_days)==null?void 0:E.lesson)||10,clinic:((L=e.advance_booking_days)==null?void 0:L.clinic)||10,buddy:((O=e.advance_booking_days)==null?void 0:O.buddy)||10},advance_booking_enabled:{court:((q=e.advance_booking_enabled)==null?void 0:q.court)||!0,lesson:((G=e.advance_booking_enabled)==null?void 0:G.lesson)||!0,clinic:((V=e.advance_booking_enabled)==null?void 0:V.clinic)||!0,buddy:((U=e.advance_booking_enabled)==null?void 0:U.buddy)||!0}}]}}console.log("Submitting membership plan data:",JSON.stringify(h,null,2));const J=await w.callRawAPI(x==="club"?"/v3/api/custom/courtmatchup/club/profile-edit":`/v3/api/custom/courtmatchup/admin/profile-edit/${(X=r==null?void 0:r.user)==null?void 0:X.id}`,h,"POST");w.setTable("activity_logs"),await w.callRestAPI({user_id:M,activity_type:re.club_ui,action_type:de.UPDATE,data:JSON.stringify(h),club_id:j==null?void 0:j.id,description:"Updated membership plans"},"POST"),J.error&&I(_,J.message||"Failed to save plan",3e3,"error"),m(!1),$(),y()}catch(h){I(_,h.message||"Failed to save plan",3e3,"error")}},y=()=>{m(!1),p(null)};return a.jsxs("div",{className:"flex flex-col gap-4 p-5",children:[a.jsx("style",{children:`
          @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
            20%, 40%, 60%, 80% { transform: translateX(4px); }
          }
          .shake {
            animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
          }
        `}),B&&a.jsx(ne,{}),a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"text-xl font-medium",children:"Membership settings"}),a.jsxs("button",{onClick:()=>{C("create"),m(!0)},className:"flex items-center gap-2 rounded-lg border bg-primaryBlue px-3 py-2 text-sm text-white ",children:[a.jsx("span",{children:"New plan"}),a.jsx(ue,{className:"h-4 w-4"})]})]}),a.jsx("div",{className:`overflow-x-auto ${t?"shake":""}`,children:a.jsxs("table",{className:"min-w-full border-separate border-spacing-y-2",children:[a.jsx("thead",{children:a.jsxs("tr",{className:"text-left text-sm text-gray-500",children:[a.jsx("th",{className:"pb-4",children:"Plan"}),a.jsx("th",{className:"pb-4",children:"Price"}),a.jsx("th",{className:"pb-4 text-center",children:"Court booking"}),a.jsx("th",{className:"pb-4 text-center",children:"Lessons"}),a.jsx("th",{className:"pb-4 text-center",children:"Clinics"}),a.jsx("th",{className:"pb-4 text-center",children:"Find a Buddy"}),a.jsx("th",{className:"pb-4 text-center",children:"My Groups"}),a.jsx("th",{className:"pb-4 text-center",children:"Sports Covered"}),a.jsx("th",{className:"pb-4 text-center",children:"Advanced Booking"})]})}),a.jsx("tbody",{children:o.length>0?o==null?void 0:o.map(e=>{var s,l;return a.jsxs("tr",{className:"overflow-hidden",children:[a.jsx("td",{className:"rounded-l-xl bg-white px-4 py-3 text-gray-600",children:e.plan_name}),a.jsx("td",{className:"bg-white px-4 py-3",children:ie(e.price)}),a.jsx("td",{className:"bg-white px-4 py-3",children:a.jsx("div",{className:"flex justify-center",children:a.jsx(c,{checked:e.allow_court,onClick:g,className:`${e.allow_court?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 cursor-not-allowed items-center rounded-full transition-colors`,children:a.jsx("span",{className:`${e.allow_court?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),a.jsx("td",{className:"bg-white px-4 py-3",children:a.jsx("div",{className:"flex justify-center",children:a.jsx(c,{checked:e.allow_coach,onClick:g,className:`${e.allow_coach?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:a.jsx("span",{className:`${e.allow_coach?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),a.jsx("td",{className:"bg-white px-4 py-3",children:a.jsx("div",{className:"flex justify-center",children:a.jsx(c,{checked:e.allow_clinic,onClick:g,className:`${e.allow_clinic?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:a.jsx("span",{className:`${e.allow_clinic?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),a.jsx("td",{className:"bg-white px-4 py-3",children:a.jsx("div",{className:"flex justify-center",children:a.jsx(c,{checked:e.allow_buddy,onClick:g,className:`${e.allow_buddy?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:a.jsx("span",{className:`${e.allow_buddy?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),a.jsx("td",{className:"bg-white px-4 py-3",children:a.jsx("div",{className:"flex items-center justify-center gap-2",children:a.jsx(c,{checked:e.allow_groups,onClick:g,className:`${e.allow_groups?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:a.jsx("span",{className:`${e.allow_groups?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),a.jsx("td",{className:"bg-white px-4 py-3",children:a.jsx("div",{className:"text-center",children:a.jsx("span",{className:"text-xs text-gray-600",children:T(e.applicable_sports)})})}),a.jsx("td",{className:"rounded-r-xl bg-white px-4 py-3",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"text-gray-500",children:a.jsx("span",{className:"whitespace-nowrap",children:((s=e.advance_booking_enabled)==null?void 0:s.court)===!1?"Disabled":`${((l=e.advance_booking_days)==null?void 0:l.court)||10}d`})}),a.jsx("button",{className:"ml-5 flex items-center justify-center text-gray-500 transition-colors hover:text-gray-700",onClick:()=>{C("edit"),A(e)},children:a.jsx(ee,{className:"h-5 w-5"})})]})})]},e.name)}):a.jsx("tr",{className:"text-center text-sm text-gray-500",children:a.jsx("td",{colSpan:"8",children:"No plans available"})})})]})}),a.jsx(ce,{isOpen:u,onClose:y,title:"Plan details",onPrimaryAction:()=>{m(!1)},showFooter:!1,children:a.jsx(be,{initialData:d,mode:v,onSubmit:e=>S(e,v),onClose:y})})]})}export{ye as M};
