import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{f as be,b as a,L as Le}from"./vendor-851db8c1.js";import{M as Se,G as Ne,A as Me,D as k,c as _e,E as O,H as U,b as f,J as Pe,t as z}from"./index-ca7cbd3e.js";import{a as ke}from"./index.esm-9c6194ba.js";import{P as Ee}from"./index-eb1bc208.js";import{l as Ae}from"./lodash-91d5d207.js";import Te from"./Skeleton-1e8bf077.js";import{T as He}from"./TimeslotPicker-e509ecbb.js";import{I as Ze}from"./InvitationCard-9a724ad6.js";import{H as De}from"./HistoryComponent-cf25af7a.js";import{F as W,f as we}from"./FormattedPhoneNumber-40dd7178.js";import{D as Ve}from"./DataTable-a2248415.js";let h=new Se;const Ie=[{header:"Name",accessor:"name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Email",accessor:"email",isSorted:!1,isSortedDesc:!1,mappingExist:!1},{header:"Phone",accessor:"phone",isSorted:!1,isSortedDesc:!1,mappingExist:!1},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{1:"Active",0:"Inactive"}},{header:"Sport",accessor:"sport_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1},{header:"Action",accessor:""}],$e=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Fe=({club:i,sports:j})=>{const q=be(),{dispatch:u}=a.useContext(Ne),{dispatch:b}=a.useContext(Me),[G,J]=a.useState([]),[o,Q]=a.useState(10),[K,X]=a.useState(0),[p,Y]=a.useState(1),[ee,te]=a.useState(!1),[se,ae]=a.useState(!1),[E,L]=a.useState(!0),[A,S]=a.useState(!1),[N,v]=a.useState([]),[n,T]=a.useState(null),[ie,H]=a.useState(!1),[ne,M]=a.useState(!1),[le,Z]=a.useState(!1),[oe,re]=a.useState(null),[y,_]=a.useState(!1),[ce,de]=a.useState(null),[me,D]=a.useState(!1),[w,P]=a.useState(""),[V,pe]=a.useState(null),[r,x]=a.useState({name:"",sport:"",status:""}),I=localStorage.getItem("user");function he(){c(p-1,o)}function ge(){c(p+1,o)}async function c(e,s,d,g={}){L(!0);try{const l=new URLSearchParams,{first_name:C,last_name:F,sport_id:R,status:B}=g;C&&l.append("first_name",C),F&&l.append("last_name",F),R&&l.append("sport_id",R),B!==void 0&&l.append("status",B),l.append("page",e),l.append("limit",s);const m=await h.callRawAPI(`/v3/api/custom/courtmatchup/reservations/coaches/${i==null?void 0:i.id}?${l.toString()}`,{},"GET");J((m==null?void 0:m.coaches)||[]),m!=null&&m.pagination&&(X(m.pagination.total_pages||0),Y(m.pagination.current_page||1),te(m.pagination.current_page>1),ae(m.pagination.current_page<m.pagination.total_pages))}catch(l){L(!1),console.log("ERROR",l),z(b,l.message)}finally{L(!1)}}const ue=()=>{const e={};r.name&&(e.first_name=r.name),r.sport&&(e.sport_id=r.sport),r.status!==""&&(e.status=r.status),c(1,o,{},e)},xe=e=>{const s=e.target.value;x({...r,status:s})},fe=e=>{const s=e.target.value;x({...r,sport:s})};a.useEffect(()=>{u({type:"SETPATH",payload:{path:"coaches"}}),i!=null&&i.id&&c(p,o)},[i==null?void 0:i.id]);const ve=async({coach_id:e,coach_user_id:s})=>{Z(!0);try{h.setTable("user"),await h.callRestAPI({id:s},"DELETE"),h.setTable("coach"),await h.callRestAPI({id:e},"DELETE"),await O(h,{user_id:I,activity_type:k.coach_management,action_type:U.DELETE,data:{coach_id:e,coach_user_id:s},club_id:i==null?void 0:i.id,description:"Deleted coach"}),f(u,"Coach deleted successfully",3e3,"success"),M(!1),c(p,o)}catch(d){console.error("Error deleting coach:",d),z(b,d.message)}Z(!1)},ye=e=>{if(T(e),S(!0),e.availability)try{const s=Ae.isArray(e.availability)?e.availability:[],d=$e.map(g=>({day:g.toLowerCase(),timeslots:[]}));s&&Array.isArray(s)&&s.length>0&&s.forEach(g=>{const l=d.findIndex(C=>C.day===g.day.toLowerCase());l!==-1&&(d[l].timeslots=g.timeslots)}),v(d)}catch(s){console.error("Error parsing availability:",s),v([])}else v([])};a.useEffect(()=>{const e=s=>{y&&!s.target.closest("#contact-info-button")&&!s.target.closest(".contact-info-popover")&&_(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[y]);const Ce=e=>{const s=e.target.value;P(s),x({...r,name:s}),V&&clearTimeout(V);const d=setTimeout(()=>{s.trim()?c(p,o,{},{first_name:s}):c(p,o)},500);pe(d)},je={name:e=>t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx("img",{src:e.photo||"/default-avatar.png",alt:`${e.first_name} ${e.last_name}`,className:"!h-10 !w-10 rounded-full object-cover",loading:"lazy"}),t.jsx("span",{className:"font-medium capitalize text-gray-900",children:!(e!=null&&e.first_name)||!(e!=null&&e.last_name)?"--":`${e==null?void 0:e.first_name} ${e==null?void 0:e.last_name}`})]}),email:e=>t.jsx("span",{className:"text-gray-600",children:(e==null?void 0:e.email)||"--"}),phone:e=>t.jsx("span",{className:"text-gray-600",children:e!=null&&e.phone?t.jsx(W,{phoneNumber:e.phone,format:"US"}):"--"}),status:e=>t.jsx("span",{className:"text-gray-600",children:(e==null?void 0:e.status)===1?"Active":"Inactive"}),sport_id:e=>t.jsx("span",{className:"text-gray-600",children:e!=null&&e.sports&&e.sports.length>0?e.sports.map(s=>s.name).join(", "):"--"}),"":e=>t.jsxs("div",{className:"flex items-center justify-end gap-3",children:[t.jsx(Le,{to:`/${$}/view-coach/${e.id}`,className:"rounded-full p-2 hover:bg-gray-100",onClick:s=>s.stopPropagation(),children:t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M6.88016 17.7625C7.97481 16.1634 9.77604 15.1243 11.9993 15.1243C14.2227 15.1243 16.0239 16.1634 17.1185 17.7625M6.88016 17.7625C8.24153 18.9726 10.0346 19.7077 11.9993 19.7077C13.9641 19.7077 15.7572 18.9726 17.1185 17.7625M6.88016 17.7625C5.29173 16.3505 4.29102 14.2918 4.29102 11.9993C4.29102 7.74215 7.74215 4.29102 11.9993 4.29102C16.2565 4.29102 19.7077 7.74215 19.7077 11.9993C19.7077 14.2918 18.707 16.3505 17.1185 17.7625M14.7077 10.3327C14.7077 11.8285 13.4951 13.041 11.9993 13.041C10.5036 13.041 9.29102 11.8285 9.29102 10.3327C9.29102 8.83691 10.5036 7.62435 11.9993 7.62435C13.4951 7.62435 14.7077 8.83691 14.7077 10.3327Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinejoin:"round"})})}),t.jsx("button",{onClick:s=>{s.stopPropagation(),ye(e)},className:"rounded-full p-2 hover:bg-gray-100",children:t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M2.46209 9.95722L3.76445 17.3433C3.86035 17.8872 4.37901 18.2503 4.9229 18.1544L10.1162 17.2387M2.46209 9.95722L1.94114 7.0028C1.84524 6.45891 2.20841 5.94025 2.7523 5.84434L14.0776 3.84739C14.6215 3.75149 15.1401 4.11466 15.236 4.65855L15.757 7.61297L2.46209 9.95722ZM16.0002 11.7509V14.0009L18.0002 16.0009M22.2502 14.0009C22.2502 17.4527 19.452 20.2509 16.0002 20.2509C12.5485 20.2509 9.75025 17.4527 9.75025 14.0009C9.75025 10.5491 12.5485 7.75092 16.0002 7.75092C19.452 7.75092 22.2502 10.5491 22.2502 14.0009Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),t.jsxs("div",{className:"relative",children:[t.jsx("button",{id:"contact-info-button",onClick:s=>{s.stopPropagation(),_(!y),de(e.id)},className:"rounded-full p-2 hover:bg-gray-100",children:t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M9.87776 11.0572L19.5007 5.70443M9.47619 11.0523L5.20388 6.53065C4.70178 5.99926 5.07851 5.125 5.8096 5.125H19.0881C19.732 5.125 20.1326 5.82416 19.807 6.3797L12.9883 18.0142C12.6164 18.6488 11.6648 18.5255 11.4668 17.8172L9.67304 11.4003C9.63664 11.27 9.56907 11.1506 9.47619 11.0523Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"square",strokeLinejoin:"round"})})}),y&&ce===e.id&&t.jsxs("div",{className:"contact-info-popover absolute right-0 z-50 mt-2 w-[400px] rounded-2xl bg-white shadow-lg",onClick:s=>{s.preventDefault(),s.stopPropagation()},children:[t.jsxs("div",{className:"p-6",children:[t.jsx("h3",{className:"mb-6 text-xl font-medium text-gray-900",children:"Contact information"}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{className:"flex items-center justify-between rounded-xl bg-[#F6F8FA] p-4",children:[t.jsxs("div",{children:[t.jsx("p",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"EMAIL"}),t.jsx("div",{className:"flex items-center justify-between gap-4",children:t.jsx("p",{className:"text-gray-900 underline",children:(e==null?void 0:e.email)||"--"})})]}),t.jsx("button",{onClick:async s=>{s.preventDefault(),s.stopPropagation(),await navigator.clipboard.writeText((e==null?void 0:e.email)||"--"),f(u,"Email copied to clipboard",3e3,"success")},className:"h-10 w-10 rounded-lg bg-white p-2 hover:bg-gray-100",children:t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M13.75 14.5V16.75C13.75 16.9489 13.671 17.1397 13.5303 17.2803C13.3897 17.421 13.1989 17.5 13 17.5H4C3.80109 17.5 3.61032 17.421 3.46967 17.2803C3.32902 17.1397 3.25 16.9489 3.25 16.75L3.25 6.25C3.25 6.05109 3.32902 5.86032 3.46967 5.71967C3.61032 5.57902 3.80109 5.5 4 5.5H6.25V3.25C6.25 2.836 6.5875 2.5 7.00525 2.5L15.9948 2.5C16.0937 2.49941 16.1917 2.51836 16.2833 2.55578C16.3748 2.5932 16.4581 2.64833 16.5283 2.71803C16.5984 2.78772 16.6542 2.87059 16.6922 2.96189C16.7303 3.05318 16.7499 3.15109 16.75 3.25L16.7477 13.75C16.7477 14.164 16.4102 14.5 15.9925 14.5H13.75ZM15.2478 13L15.25 4L7.75 4L7.75 13H15.2478ZM12.25 14.5L6.25 14.5L6.25 7H4.75L4.75 16L12.25 16V14.5Z",fill:"#525866"})})})]}),t.jsxs("div",{className:"flex items-center justify-between rounded-xl bg-[#F6F8FA] p-4",children:[t.jsxs("div",{children:[t.jsx("p",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"PHONE"}),t.jsx("div",{className:"flex items-center justify-between gap-4",children:t.jsx("p",{className:"text-gray-900 underline",children:e!=null&&e.phone?t.jsx(W,{phoneNumber:e.phone,format:"US"}):"--"})})]}),t.jsx("button",{onClick:async()=>{const s=e!=null&&e.phone?we(e.phone,"US"):"--";await navigator.clipboard.writeText(s),f(u,"Phone copied to clipboard",3e3,"success")},className:"h-10 w-10 rounded-lg bg-white p-2 hover:bg-gray-100",children:t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M13.75 14.5V16.75C13.75 16.9489 13.671 17.1397 13.5303 17.2803C13.3897 17.421 13.1989 17.5 13 17.5H4C3.80109 17.5 3.61032 17.421 3.46967 17.2803C3.32902 17.1397 3.25 16.9489 3.25 16.75L3.25 6.25C3.25 6.05109 3.32902 5.86032 3.46967 5.71967C3.61032 5.57902 3.80109 5.5 4 5.5H6.25V3.25C6.25 2.836 6.5875 2.5 7.00525 2.5L15.9948 2.5C16.0937 2.49941 16.1917 2.51836 16.2833 2.55578C16.3748 2.5932 16.4581 2.64833 16.5283 2.71803C16.5984 2.78772 16.6542 2.87059 16.6922 2.96189C16.7303 3.05318 16.7499 3.15109 16.75 3.25L16.7477 13.75C16.7477 14.164 16.4102 14.5 15.9925 14.5H13.75ZM15.2478 13L15.25 4L7.75 4L7.75 13H15.2478ZM12.25 14.5L6.25 14.5L6.25 7H4.75L4.75 16L12.25 16V14.5Z",fill:"#525866"})})})]})]})]}),t.jsx("div",{className:"flex justify-end border-t border-gray-200 p-5",children:t.jsx("button",{onClick:s=>{s.preventDefault(),s.stopPropagation(),_(!1)},className:"rounded-xl bg-[#176448] px-5 py-3 text-white hover:bg-[#176448]",children:"Close"})})]})]}),t.jsx("button",{onClick:s=>{s.stopPropagation(),M(!0),re({coach_id:e.id,coach_user_id:e.user_id})},className:"rounded-full p-2 hover:bg-gray-100",children:t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M4.74071 16.928L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49738 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49738 4.97796Z",fill:"#868C98"})})})]})},$=localStorage.getItem("role");return t.jsxs("div",{className:"h-screen px-8",children:[t.jsx("div",{className:"flex flex-col flex-wrap justify-between gap-4 py-3 md:flex-row md:items-center",children:t.jsxs("div",{className:"w-full",children:[t.jsxs("div",{className:"mb-4 flex flex-col justify-between gap-4 sm:flex-row sm:items-center",children:[t.jsx("h3",{className:"text-lg font-medium",children:"Coach Filters"}),t.jsxs("div",{className:"flex gap-2",children:[t.jsxs("button",{onClick:()=>D(!0),className:"inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[t.jsx("span",{children:"+"}),"Add new"]}),t.jsx(De,{title:"Coach History",emptyMessage:"No coach history found",activityType:k.coach_management})]})]}),t.jsxs("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:[t.jsxs("div",{children:[t.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"Name"}),t.jsxs("div",{className:"relative flex items-center",children:[t.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:t.jsx(_e,{className:"text-gray-500"})}),t.jsx("input",{type:"text",value:w,onChange:Ce,className:"w-full rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search coaches by name"}),w&&t.jsx("button",{onClick:()=>{P(""),x({...r,name:""}),c(p,o)},className:"absolute right-2 rounded-full p-1 hover:bg-gray-100",children:t.jsx(ke,{className:"text-gray-500"})})]})]}),t.jsxs("div",{children:[t.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"Sport"}),t.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700",value:r.sport,onChange:fe,children:[t.jsx("option",{value:"",children:"All Sports"}),j==null?void 0:j.map(e=>t.jsx("option",{value:e.id,children:e.name},e.id))]})]}),t.jsxs("div",{children:[t.jsx("label",{className:"mb-1 block text-xs font-medium text-gray-700",children:"Status"}),t.jsxs("select",{className:"w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700",value:r.status,onChange:xe,children:[t.jsx("option",{value:"",children:"All Statuses"}),t.jsx("option",{value:"0",children:"Inactive"}),t.jsx("option",{value:"1",children:"Active"})]})]}),t.jsxs("div",{className:"col-span-full mt-4 flex justify-end gap-3",children:[t.jsx("button",{onClick:()=>{x({name:"",sport:"",status:""}),P(""),c(1,o)},className:"rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Clear Filters"}),t.jsx("button",{onClick:()=>ue(),className:"rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Apply Filters"})]})]})]})}),E?t.jsx(Te,{}):t.jsx(Ve,{columns:Ie,data:G,loading:E,renderCustomCell:je,rowClassName:"hover:bg-gray-50 bg-gray-100 px-4 py-3 cursor-pointer",emptyMessage:"No coaches available",loadingMessage:"Loading coaches...",onClick:e=>q(`/${$}/view-coach/${e.id}`)}),t.jsx(Ee,{currentPage:p,pageCount:K,pageSize:o,canPreviousPage:ee,canNextPage:se,updatePageSize:e=>{Q(e),c(p,e)},previousPage:he,nextPage:ge,gotoPage:e=>c(e,o)}),A&&n&&t.jsx(He,{showTimesAvailableModal:A,setShowTimesAvailableModal:S,selectedTimes:N,setSelectedTimes:v,title:`${n==null?void 0:n.first_name} ${n==null?void 0:n.last_name}'s availability`,isSubmitting:ie,onSave:async()=>{try{H(!0);const s=(()=>N?N.filter(d=>d.timeslots.length>0):[])();h.setTable("coach"),await h.callRestAPI({id:n.id,availability:JSON.stringify(s)},"PUT"),await O(h,{user_id:I,activity_type:k.coach_management,action_type:U.UPDATE,data:{coach_id:n.id,availability:s},club_id:i==null?void 0:i.id,description:`Updated coach availability for ${n==null?void 0:n.first_name} ${n==null?void 0:n.last_name}`}),f(u,"Availability updated successfully",3e3,"success"),c(p,o)}catch(e){console.error("Error saving availability:",e),f(b,e.message,3e3,"error")}finally{H(!1),S(!1),T(null)}}}),t.jsx(Pe,{isOpen:ne,onClose:()=>M(!1),onDelete:()=>ve(oe),message:"Are you sure you want to delete this coach?",loading:le,title:"Delete Coach"}),me&&t.jsx(Ze,{title:"Invite Coach",onClose:()=>D(!1),link:`${window.location.origin}/coach/signup?club_id=${i==null?void 0:i.id}`,message:"Please use this following link to invite a Coach to join our website. Once the Coach has signed up,their details can be fetched and managed in our system."})]})},Ye=Fe;export{Ye as L};
