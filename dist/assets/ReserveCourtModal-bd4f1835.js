import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as x}from"./vendor-851db8c1.js";import{u,al as m,d}from"./index-ca7cbd3e.js";const h=({isOpen:n,onClose:i,onReserveCourt:a,loading:r=!1})=>{const{user_subscription:l,user_permissions:s}=u(),[t,o]=x.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),c=()=>{if(!(l!=null&&l.planId)){o({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to join buddy requests",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(s!=null&&s.allowBuddy)){o({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${s==null?void 0:s.planName}) does not include the Find a Buddy feature. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}a()};return e.jsxs(e.Fragment,{children:[e.jsx(m,{isOpen:t.isOpen,onClose:()=>o({...t,isOpen:!1}),title:t.title,message:t.message,actionButtonText:t.actionButtonText,actionButtonLink:t.actionButtonLink,type:t.type}),e.jsxs("div",{className:`fixed inset-0 z-[9999] flex items-center justify-center ${n?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-3xl bg-white",children:[e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Reserve court"}),e.jsxs("div",{className:"flex items-start justify-center rounded-xl bg-[#F17B2C] p-3",children:[e.jsx("div",{className:"mr-2",children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62127L13.9236 12.1348C13.9737 12.2225 14 12.3219 14 12.4231C14 12.5243 13.9737 12.6237 13.9236 12.7114C13.8736 12.799 13.8017 12.8718 13.715 12.9224C13.6283 12.973 13.5301 12.9997 13.43 12.9997H2.57C2.46995 12.9997 2.37165 12.973 2.285 12.9224C2.19835 12.8718 2.12639 12.799 2.07636 12.7114C2.02634 12.6237 2 12.5243 2 12.4231C2 12.3219 2.02634 12.2225 2.07637 12.1348L7.50636 2.62127C7.5564 2.53363 7.62835 2.46085 7.715 2.41025C7.80165 2.35965 7.89995 2.33301 8 2.33301C8.10005 2.33301 8.19835 2.35965 8.285 2.41025C8.37165 2.46085 8.4436 2.53363 8.49364 2.62127ZM7.42998 10.1168V11.2699H8.57002V10.1168H7.42998ZM7.42998 6.08074V8.96363H8.57002V6.08074H7.42998Z",fill:"white"})})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-white",children:"IMPORTANT"}),e.jsx("p",{className:"text-sm text-white",children:"By reserving a court, you agree to share your contact information so the request owner can get in touch with you."})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t px-6 py-4",children:[e.jsx("button",{onClick:i,className:"rounded-xl border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),e.jsx(d,{onClick:c,className:"rounded-xl bg-green-800 px-4 py-2 text-white hover:bg-green-700",loading:r,children:"Reserve court"})]})]})]})]})},f=({isOpen:n,onClose:i,onDelete:a,loading:r=!1})=>e.jsxs("div",{className:`fixed inset-0 z-50 flex items-center justify-center ${n?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-3xl bg-white",children:[e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Cancel reservation"}),e.jsx("div",{className:"flex items-start justify-center rounded-xl bg-[#F17B2C] p-3",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-white",children:"IMPORTANT"}),e.jsx("p",{className:"text-sm text-white",children:"Are you sure you want to cancel this reservation?"})]})})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t px-6 py-4",children:[e.jsx("button",{onClick:i,className:"rounded-xl border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),e.jsx(d,{onClick:a,className:"rounded-xl bg-green-800 px-4 py-2 text-white hover:bg-green-700",loading:r,children:"Cancel reservation"})]})]})]}),v=h;export{f as C,v as R};
