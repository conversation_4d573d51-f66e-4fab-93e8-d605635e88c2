import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as n,f as ge,L as fe}from"./vendor-851db8c1.js";import{a2 as Y,G as Q,A as te,R as le,d as se,M as X,b as Z,u as re,al as je,aa as we,a4 as K,aD as ne,aq as ve,aI as Ne,e as ye,T as Ce,t as xe}from"./index-ca7cbd3e.js";import{o as be,g as Se,f as _e}from"./date-fns-07266b7d.js";import{T as Re}from"./TimeSlots-c9c10356.js";import{C as ke}from"./CalendarIcon-b3488133.js";import{R as Te}from"./ReserveCourtModal-bd4f1835.js";import{C as Me}from"./Calendar-282b3fcf.js";import{h as De}from"./moment-a9aaa855.js";import{f as Le}from"./index.esm-b72032a7.js";import{u as Be,C as V}from"./react-hook-form-687afde5.js";import{c as Ee,a as J,f as Pe,e as de}from"./yup-54691517.js";import{o as $e}from"./yup-2824f222.js";import{A as Oe}from"./AccessRestricted-eb1272e0.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./@hookform/resolvers-67648cca.js";function Fe({title:l,titleId:d,...s},y){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:y,"aria-labelledby":d},s),l?n.createElement("title",{id:d},l):null,n.createElement("path",{fillRule:"evenodd",d:"M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z",clipRule:"evenodd"}))}const Ae=n.forwardRef(Fe),Ie=Ae;function Ue({title:l,titleId:d,...s},y){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:y,"aria-labelledby":d},s),l?n.createElement("title",{id:d},l):null,n.createElement("path",{fillRule:"evenodd",d:"M5.47 5.47a.75.75 0 0 1 1.06 0L12 10.94l5.47-5.47a.75.75 0 1 1 1.06 1.06L13.06 12l5.47 5.47a.75.75 0 1 1-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 0 1-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 0 1 0-1.06Z",clipRule:"evenodd"}))}const We=n.forwardRef(Ue),ze=We;function ie({buddy:l,onSelect:d,clubSports:s}){var y;return e.jsx("button",{onClick:()=>d(l),className:"group w-full cursor-pointer rounded-xl bg-gray-50 p-3 transition-all duration-200 hover:scale-[1.01] hover:bg-gray-100 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:p-4",children:e.jsxs("div",{className:"flex gap-3 sm:gap-4",children:[e.jsx("div",{className:"w-20 flex-shrink-0 sm:w-24 md:w-32",children:e.jsx("img",{src:(l==null?void 0:l.owner_photo)||"/default-avatar.png",alt:`${l==null?void 0:l.owner_first_name} ${l==null?void 0:l.owner_last_name}`,className:"aspect-square w-full rounded-full object-cover shadow-sm",loading:"lazy"})}),e.jsxs("div",{className:"flex min-w-0 flex-1 flex-col",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"min-w-0",children:[e.jsxs("p",{className:"mb-2 break-words text-sm sm:text-base",children:[(()=>{const x=new Date(l.date),u=x.toLocaleDateString("en-US",{weekday:"long"}),p=x.toLocaleDateString("en-US",{month:"short",day:"numeric"});return be(x)?`Today (${u})`:`${u}, ${p}`})()," ","• ",Y(l.start_time)," -"," ",Y(l.end_time)]}),e.jsxs("div",{className:"mb-2 flex flex-wrap items-center text-xs text-gray-500 sm:text-sm",children:[e.jsxs("span",{children:["Added ",Se(new Date(l==null?void 0:l.create_at))," ago"," "]})," "," "," by",e.jsxs("span",{className:"ml-1 capitalize text-gray-700",children:[l==null?void 0:l.owner_first_name," ",l==null?void 0:l.owner_last_name]})]})]}),e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mt-1 flex-shrink-0 self-start text-gray-400 transition-transform duration-200 group-hover:translate-x-1 sm:mt-0 sm:h-5 sm:w-5",children:e.jsx("path",{d:"M4.16669 10H15.8334M15.8334 10L10 4.16669M15.8334 10L10 15.8334",stroke:"currentColor",strokeWidth:"1.67",strokeLinecap:"round",strokeLinejoin:"round"})})]}),e.jsxs("div",{className:"mt-auto flex flex-wrap items-center gap-1",children:[(l==null?void 0:l.sport_id)&&e.jsx("span",{className:"mb-1 rounded-full border bg-white px-2 py-1 text-xs text-gray-600",children:(y=s.find(x=>x.id===(l==null?void 0:l.sport_id)))==null?void 0:y.name}),e.jsxs("span",{className:"mb-1 rounded-full border bg-white px-2 py-1 text-xs text-gray-600",children:["NTRP: ",l.ntrp," - ",l.max_ntrp]}),e.jsxs("span",{className:"mb-1 flex items-center justify-center gap-1 rounded-full border bg-white px-2 py-1 text-xs text-gray-600",children:[e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.99998 1.33301C6.34313 1.33301 4.99998 2.67615 4.99998 4.33301C4.99998 5.98986 6.34313 7.33301 7.99998 7.33301C9.65683 7.33301 11 5.98986 11 4.33301C11 2.67615 9.65683 1.33301 7.99998 1.33301Z",fill:"#525866"}),e.jsx("path",{d:"M8.00025 8.33301C5.21805 8.33301 3.18228 10.1954 2.62678 12.6672C2.46036 13.4077 3.06674 13.9997 3.73252 13.9997H12.268C12.9338 13.9997 13.5401 13.4077 13.3737 12.6672C12.8182 10.1954 10.7824 8.33301 8.00025 8.33301Z",fill:"#525866"})]}),l.num_players,"/",l.num_needed]})]})]})]})},l.id)}let he=new X;function ce({isOpen:l,onClose:d,buddy:s,onRequestJoin:y,onReserveCourt:x,fetchData:u,setSelectedBuddy:p,clubSports:w}){var h,M,I,H,F,a;const{dispatch:f}=n.useContext(Q);n.useContext(te),ge(),n.useState(!1);const[v,N]=n.useState("details"),[P,R]=n.useState(!1),[$,D]=n.useState(s==null?void 0:s.date),[r,C]=n.useState(!1),[A,O]=n.useState(!1),[U,k]=n.useState(null),[E,c]=n.useState([{from:s==null?void 0:s.start_time,until:s==null?void 0:s.end_time}]),b=async()=>{try{const t=await he.callRawAPI(`/v3/api/custom/courtmatchup/user/buddy/${s.id}`,"PUT",{date:$,start_time:E.from,end_time:E.until});Z(f,"Date updated successfully",3e3,"success"),u()}catch(t){console.error("Error updating date:",t),Z(f,"Error updating date",3e3,"error")}finally{C(!1)}},S=t=>{c([{from:t.from,until:t.until}])};if(!s)return null;const L=(h=s==null?void 0:s.requests_to_join)==null?void 0:h.filter(t=>t.request_status==0||t.request_status==4),_=(M=s==null?void 0:s.requests_to_join)==null?void 0:M.filter(t=>t.request_status==1),m=(I=s==null?void 0:s.requests_to_join)==null?void 0:I.filter(t=>t.request_status==2);(H=s==null?void 0:s.requests_to_join)==null||H.filter(t=>t.request_status==4);const T=async(t,o)=>{const j=s.requests_to_join.find(W=>W.request_id===t);if(o===0){k({requestId:t,status:o,userName:`${j.first_name} ${j.last_name}`}),O(!0);return}await B(t,o)},B=async(t,o)=>{C(!0);try{if(!(await he.callRawAPI("/v3/api/custom/courtmatchup/user/buddy/update-request",{request_id:t,status:o},"POST")).error){u(),Z(f,"Request status updated successfully",3e3,"success");const W=s.requests_to_join.map(G=>G.request_id===t?{...G,request_status:o}:G);p({...s,requests_to_join:W})}}catch(j){console.error(j),Z(f,j.message,3e3,"error")}finally{C(!1),O(!1),k(null)}};return e.jsxs(e.Fragment,{children:[e.jsxs(le,{isOpen:l,onClose:d,title:"Request details",showFooter:!1,className:"!p-0",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"px-5 pt-5",children:e.jsxs("div",{className:"flex w-full justify-between gap-2 rounded-xl bg-gray-100 p-1",children:[e.jsx("button",{onClick:()=>N("details"),className:`flex w-full items-center justify-center rounded-lg border border-transparent bg-transparent px-4 py-2 text-sm font-medium ${v==="details"?"border-gray-200 bg-white":""}`,children:"Details"}),e.jsxs("button",{onClick:()=>N("requests"),className:`flex w-full items-center justify-center rounded-lg border border-transparent bg-transparent px-4 py-2 text-sm font-medium ${v==="requests"?"border-gray-200 bg-white":""}`,children:["Requests (",(F=s==null?void 0:s.requests_to_join)==null?void 0:F.length,")"]})]})}),v==="details"?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"px-5",children:e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"REQUEST MADE"}),e.jsxs("p",{className:"mt-1 font-medium",children:[new Date(s==null?void 0:s.create_at).toLocaleDateString("en-US",{weekday:"long",month:"short",day:"numeric"})," • ",Y(s==null?void 0:s.start_time)," -"," ",Y(s==null?void 0:s.end_time)]})]})}),e.jsx("div",{className:"px-5",children:e.jsxs("div",{className:"space-y-4 divide-y",children:[e.jsxs("div",{className:"py-4",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"DATE & TIME"}),e.jsxs("button",{onClick:()=>R(!0),className:"flex items-center gap-2 rounded-lg border px-2 py-1 text-sm text-gray-500 hover:bg-gray-50",children:[e.jsx(ke,{className:"h-5 w-5"}),"Select new date"]}),P&&e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"w-full max-w-md rounded-xl bg-white p-4",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Select new date and time"}),e.jsx("button",{onClick:()=>R(!1),className:"rounded-lg p-1 hover:bg-gray-100",children:e.jsx(ze,{className:"h-5 w-5 text-gray-500"})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Date"}),e.jsx("input",{type:"date",value:$,onChange:t=>D(t.target.value),className:"mt-1 block w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Time slot"}),e.jsx("div",{className:"mt-2",children:e.jsx(Re,{selectedDate:$,timeRange:E,onTimeClick:S,isTimeSlotAvailable:t=>!0,startHour:0,endHour:23,interval:30})})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{onClick:()=>R(!1),className:"rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:b,className:"rounded-lg bg-[#1B254B] px-4 py-2 text-sm font-medium text-white hover:bg-blue-900",children:"Save changes"})]})]})]})})]}),e.jsxs("p",{className:"mt-1 font-medium",children:[new Date(s==null?void 0:s.date).toLocaleDateString("en-US",{weekday:"long",month:"short",day:"numeric"})," • ",Y(s==null?void 0:s.start_time)," -"," ",Y(s==null?void 0:s.end_time)]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"SPORT & TYPE"}),e.jsxs("p",{className:"mt-1 font-medium",children:[(a=w==null?void 0:w.find(t=>t.id===(s==null?void 0:s.sport_id)))==null?void 0:a.name," ",(s==null?void 0:s.type)&&`• ${s==null?void 0:s.type}`," ",(s==null?void 0:s.sub_type)&&`• ${s==null?void 0:s.sub_type}`]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"NTRP"}),e.jsxs("p",{className:"mt-1 font-medium",children:[" ",s==null?void 0:s.ntrp," - ",s==null?void 0:s.max_ntrp]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"LOOKING FOR PLAYERS"}),e.jsxs("p",{className:"mt-1 font-medium",children:[" ",s==null?void 0:s.num_needed,"/",s==null?void 0:s.num_players]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"GROUP BIO"}),e.jsx("p",{className:"mt-1 text-sm",children:s!=null&&s.notes?s==null?void 0:s.notes:"No bio provided"})]})]})})]}):e.jsx("div",{className:"px-5",children:e.jsxs("div",{className:"space-y-6",children:[L.length>0?e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Pending"}),e.jsx("div",{className:"mt-4 space-y-4",children:L.map((t,o)=>e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full bg-gray-200",children:e.jsx("img",{src:t==null?void 0:t.photo,alt:"Arthur Taylor",className:"h-full w-full object-cover"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"font-medium",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]}),e.jsx("p",{className:"text-sm text-gray-500",children:t==null?void 0:t.email})]}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-500",children:["NTRP: ",t==null?void 0:t.ntrp]})})]}),e.jsxs("div",{className:"mt-3 flex gap-2",children:[e.jsx(se,{onClick:()=>{T(t==null?void 0:t.request_id,1)},loading:r,className:"flex-1 rounded-lg border border-green-500 bg-green-50 py-2 text-sm font-medium text-green-600",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("svg",{width:"17",height:"16",viewBox:"0 0 17 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.08716 14C4.77336 14 2.08716 11.3138 2.08716 8C2.08716 4.6862 4.77336 2 8.08716 2C11.401 2 14.0872 4.6862 14.0872 8C14.0872 11.3138 11.401 14 8.08716 14ZM7.48896 10.4L11.731 6.1574L10.8826 5.309L7.48896 8.7032L5.79156 7.0058L4.94316 7.8542L7.48896 10.4Z",fill:"#38C793"})}),e.jsx("span",{children:"Accept"})]})}),e.jsx(se,{onClick:()=>{T(t==null?void 0:t.request_id,2)},loading:r,className:"flex-1 rounded-lg border border-red-500 bg-red-50 py-2 text-sm font-medium text-red-600",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("svg",{width:"17",height:"16",viewBox:"0 0 17 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.28491 8.00065C2.28491 4.31875 5.26968 1.33398 8.95158 1.33398C12.6335 1.33398 15.6182 4.31875 15.6182 8.00065C15.6182 11.6825 12.6335 14.6673 8.95158 14.6673C5.26968 14.6673 2.28491 11.6825 2.28491 8.00065ZM7.30513 5.6471C7.10987 5.45184 6.79329 5.45184 6.59803 5.6471C6.40276 5.84236 6.40276 6.15894 6.59803 6.3542L8.24447 8.00065L6.59803 9.6471C6.40276 9.84236 6.40276 10.1589 6.59803 10.3542C6.79329 10.5495 7.10987 10.5495 7.30513 10.3542L8.95158 8.70776L10.598 10.3542C10.7933 10.5495 11.1099 10.5495 11.3051 10.3542C11.5004 10.1589 11.5004 9.84236 11.3051 9.6471L9.65869 8.00065L11.3051 6.3542C11.5004 6.15894 11.5004 5.84236 11.3051 5.6471C11.1099 5.45184 10.7933 5.45184 10.598 5.6471L8.95158 7.29354L7.30513 5.6471Z",fill:"#DF1C41"})}),e.jsx("span",{children:"Decline"})]})})]})]},o))})]}):null,_.length>0&&e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Accepted"}),e.jsx("div",{className:"mt-4 space-y-4",children:_.map((t,o)=>e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full bg-gray-200",children:e.jsx("img",{src:(t==null?void 0:t.photo)||"/default-avatar.png",alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"font-medium",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]}),e.jsx("p",{className:"text-sm text-gray-500",children:t==null?void 0:t.email})]}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-500",children:["NTRP: ",t==null?void 0:t.ntrp]})})]}),e.jsxs("div",{className:"mt-3 flex items-center gap-3",children:[e.jsxs("span",{className:"flex items-center gap-1 rounded-lg border border-gray-200 bg-white px-2 py-1 text-sm text-gray-500",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.00004 1.33398C4.31814 1.33398 1.33337 4.31875 1.33337 8.00065C1.33337 11.6825 4.31814 14.6673 8.00004 14.6673C11.6819 14.6673 14.6667 11.6825 14.6667 8.00065C14.6667 4.31875 11.6819 1.33398 8.00004 1.33398ZM10.387 6.6506C10.5619 6.43688 10.5304 6.12187 10.3167 5.94701C10.1029 5.77214 9.78793 5.80364 9.61306 6.01737L6.96292 9.25643L6.02026 8.31376C5.825 8.1185 5.50842 8.1185 5.31315 8.31376C5.11789 8.50903 5.11789 8.82561 5.31315 9.02087L6.64649 10.3542C6.74638 10.4541 6.88386 10.5071 7.02495 10.5C7.16604 10.493 7.29757 10.4266 7.38702 10.3173L10.387 6.6506Z",fill:"#868C98"})}),"Accepted"]}),e.jsx("button",{onClick:()=>{T(t==null?void 0:t.request_id,0)},className:"text-sm text-gray-500 hover:text-gray-900",children:"Undo"})]})]},o))})]}),m.length>0&&e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Declined"}),e.jsx("div",{className:"mt-4 space-y-4",children:m.map((t,o)=>e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full bg-gray-200",children:e.jsx("img",{src:(t==null?void 0:t.photo)||"/default-avatar.png",alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"font-medium",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]}),e.jsx("p",{className:"text-sm text-gray-500",children:t==null?void 0:t.email})]}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-500",children:["NTRP: ",t==null?void 0:t.ntrp]})})]}),e.jsxs("div",{className:"mt-3 flex items-center gap-3",children:[e.jsxs("span",{className:"text-sm text-gray-500",children:[e.jsx("span",{className:"mr-1 inline-block h-2 w-2 rounded-full bg-gray-400"}),"Declined"]}),e.jsx("button",{onClick:()=>{T(t==null?void 0:t.request_id,4)},className:"text-sm text-gray-500 hover:text-gray-900",children:"Undo"})]})]},o))})]})]})})]}),e.jsx("div",{className:"fixed bottom-0 w-full border-t border-gray-200 bg-white px-5 py-4",children:e.jsx("button",{onClick:()=>x(s),className:"w-full rounded-xl bg-[#1B254B] py-3 text-center font-medium text-white hover:bg-blue-900",children:"Reserve court"})})]}),A&&U&&e.jsx("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"w-full max-w-md rounded-xl bg-white p-6",children:[e.jsx("h3",{className:"mb-4 text-xl font-semibold",children:"Undo Declined"}),e.jsxs("p",{className:"mb-6 text-gray-600",children:["Are you sure you want to undo decline for request from"," ",U.userName,"?"]}),e.jsx("p",{className:"mb-6 text-gray-600",children:"It will go back to Pending status."}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:()=>{O(!1),k(null)},className:"rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:()=>B(U.requestId,U.status),className:"rounded-lg bg-[#1B254B] px-4 py-2 text-sm font-medium text-white hover:bg-blue-900",children:"Yes, undo decline"})]})]})})]})}const He=({isOpen:l,onClose:d,onRequestJoin:s,loading:y=!1})=>{const{user_subscription:x,user_permissions:u}=re(),[p,w]=n.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),f=()=>{if(!(x!=null&&x.planId)){w({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to join buddy requests",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(u!=null&&u.allowBuddy)){w({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${u==null?void 0:u.planName}) does not include the Find a Buddy feature. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}s()};return e.jsxs(e.Fragment,{children:[e.jsx(je,{isOpen:p.isOpen,onClose:()=>w({...p,isOpen:!1}),title:p.title,message:p.message,actionButtonText:p.actionButtonText,actionButtonLink:p.actionButtonLink,type:p.type}),e.jsxs("div",{className:`fixed inset-0 z-50 flex items-center justify-center ${l?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-3xl bg-white",children:[e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Request to join"}),e.jsxs("div",{className:"flex items-start justify-center rounded-xl bg-[#F17B2C] p-3",children:[e.jsx("div",{className:"mr-2",children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62127L13.9236 12.1348C13.9737 12.2225 14 12.3219 14 12.4231C14 12.5243 13.9737 12.6237 13.9236 12.7114C13.8736 12.799 13.8017 12.8718 13.715 12.9224C13.6283 12.973 13.5301 12.9997 13.43 12.9997H2.57C2.46995 12.9997 2.37165 12.973 2.285 12.9224C2.19835 12.8718 2.12639 12.799 2.07636 12.7114C2.02634 12.6237 2 12.5243 2 12.4231C2 12.3219 2.02634 12.2225 2.07637 12.1348L7.50636 2.62127C7.5564 2.53363 7.62835 2.46085 7.715 2.41025C7.80165 2.35965 7.89995 2.33301 8 2.33301C8.10005 2.33301 8.19835 2.35965 8.285 2.41025C8.37165 2.46085 8.4436 2.53363 8.49364 2.62127ZM7.42998 10.1168V11.2699H8.57002V10.1168H7.42998ZM7.42998 6.08074V8.96363H8.57002V6.08074H7.42998Z",fill:"white"})})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-white",children:"IMPORTANT"}),e.jsx("p",{className:"text-sm text-white",children:"By sending request you agree to share your contact information so the request owner can get in touch with you."})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t px-6 py-4",children:[e.jsx("button",{onClick:d,className:"rounded-xl border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),e.jsx(se,{onClick:f,className:"rounded-xl bg-green-800 px-4 py-2 text-white hover:bg-green-700",loading:y,children:"Send request"})]})]})]})]})},Ze=He;new X;function Ve({isOpen:l,onClose:d,buddy:s,onRequestJoin:y,clubSports:x}){var D;const{dispatch:u}=n.useContext(Q),{dispatch:p}=n.useContext(te),[w,f]=n.useState([]);ge();const[v,N]=n.useState(!1),[P,R]=n.useState("details");async function $(r){try{const C=await we(u,p,"user",JSON.parse(r==null?void 0:r.player_ids),"user|user_id");f(C.list)}catch(C){console.error(C)}}return n.useEffect(()=>{$(s)},[s]),s?(console.log(s),e.jsx(le,{isOpen:l,onClose:d,title:"Request details",showFooter:!1,className:"!p-0",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:" px-5 pt-5",children:e.jsx("div",{className:"flex w-full justify-between gap-2 rounded-xl bg-gray-100 p-1",children:e.jsx("button",{onClick:()=>R("details"),className:`flex w-full items-center justify-center rounded-lg border border-transparent bg-transparent  px-4 py-2 text-sm font-medium ${P==="details"?"border-gray-200 bg-white":""}`,children:"Details"})})}),e.jsx("div",{className:"bg-gray-100 px-5 py-1 ",children:e.jsx("p",{className:"text-sm text-gray-500",children:"REQUESTED BY"})}),e.jsxs("div",{className:"px-5 py-1",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full",children:e.jsx("img",{src:(s==null?void 0:s.owner_photo)||"/default-avatar.png",alt:`${s==null?void 0:s.owner_first_name} ${s==null?void 0:s.owner_last_name}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium capitalize",children:[s==null?void 0:s.owner_first_name," ",s==null?void 0:s.owner_last_name]}),e.jsxs("p",{className:"text-sm text-gray-500",children:["NTRP: ",s==null?void 0:s.ntrp]})]})]}),e.jsxs("div",{className:"mt-4 rounded-xl bg-gray-100",children:[e.jsxs("button",{onClick:()=>N(!v),className:"flex w-full items-center gap-2 px-4 py-3",children:[e.jsx(Ie,{className:`h-5 w-5 transform transition-transform ${v?"rotate-90":""}`}),e.jsxs("span",{className:"text-sm font-medium",children:["Others(",w.length,")"]})]}),v&&e.jsx("div",{className:"px-4 pb-3",children:w.map(r=>e.jsxs("div",{className:"mb-3 flex items-center gap-4",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full",children:e.jsx("img",{src:(r==null?void 0:r.photo)||"/default-avatar.png",alt:`${r==null?void 0:r.first_name} ${r==null?void 0:r.last_name}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{children:[e.jsxs("p",{className:"",children:[r==null?void 0:r.first_name," ",r==null?void 0:r.last_name]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["NTPR: ",r==null?void 0:r.ntrp]})]})]}))})]})]}),e.jsx("div",{className:"bg-gray-100 px-5 py-1 ",children:e.jsx("p",{className:"text-sm text-gray-500",children:" DETAILS"})}),e.jsxs("div",{className:"space-y-0 divide-y px-5 py-1",children:[e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"DATE & TIME"}),e.jsxs("p",{className:"mt-1 font-medium",children:[new Date(s==null?void 0:s.date).toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric"})," • ",Y(s==null?void 0:s.start_time)," -"," ",Y(s==null?void 0:s.end_time)]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"SPORT & TYPE"}),e.jsxs("p",{className:"mt-1 font-medium capitalize",children:[(D=x==null?void 0:x.find(r=>r.id===(s==null?void 0:s.sport_id)))==null?void 0:D.name," ",(s==null?void 0:s.type)&&`• ${s==null?void 0:s.type}`," ",(s==null?void 0:s.sub_type)&&`• ${s==null?void 0:s.sub_type}`]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"LOOKING FOR PLAYERS"}),e.jsxs("p",{className:"mt-1 font-medium",children:[s==null?void 0:s.num_players,"/",s==null?void 0:s.num_needed]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"NTRP Range"}),e.jsxs("p",{className:"mt-1 font-medium",children:[s==null?void 0:s.ntrp," - ",s==null?void 0:s.max_ntrp]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"GROUP INFO"}),e.jsx("p",{className:"mt-1 text-sm",children:(s==null?void 0:s.notes)||"No description provided"})]})]}),e.jsx("div",{className:"fixed bottom-0 w-full border-t border-gray-200 bg-white px-10 py-5",children:e.jsxs("button",{onClick:()=>{y(s)},className:"flex w-full items-center justify-center gap-2 rounded-xl bg-[#1B254B] py-3 text-center text-white hover:bg-blue-900",children:[e.jsx("span",{children:e.jsx("svg",{width:"21",height:"20",viewBox:"0 0 21 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.25 11.5H9.75C8.5197 11.4995 7.31267 11.8354 6.25941 12.4712C5.20614 13.107 4.3467 14.0186 3.774 15.1075C3.7579 14.9054 3.7499 14.7027 3.75 14.5C3.75 10.3578 7.10775 7 11.25 7V2.875L19.125 9.25L11.25 15.625V11.5ZM9.75 10H12.75V12.481L16.7408 9.25L12.75 6.019V8.5H11.25C10.3877 8.49903 9.53535 8.68436 8.75129 9.04332C7.96724 9.40227 7.26999 9.92637 6.70725 10.5797C7.67574 10.1959 8.70822 9.99919 9.75 10Z",fill:"white"})})}),e.jsx("span",{children:"Request to join"})]})})]})})):null}let q=new X;function Je({fetchData:l,buddyData:d,setBuddyData:s,clubSports:y}){const[x,u]=n.useState("desc"),[p,w]=n.useState(!1),[f,v]=n.useState(null),[N,P]=n.useState(!1),[R,$]=n.useState(!1),{dispatch:D}=n.useContext(Q);n.useContext(te);const[r,C]=n.useState(null),[A,O]=n.useState(null),[U,k]=n.useState(!1),[E,c]=n.useState(!1),[b,S]=n.useState({num_needed:null,ntrp:null,max_ntrp:null}),L=(a,t)=>{S(t==="all"?o=>{const j={...o};return delete j[a],j}:o=>({...o,[a]:parseInt(t)}))},_=a=>{u(a),w(!1)},T=(()=>{let a=d.filter(t=>{let o=!0;return Object.entries(b).forEach(([j,W])=>{W!==null&&(o=o&&t[j]===W)}),o});return a=[...a].sort((t,o)=>{const j=new Date(t.date),W=new Date(o.date);return x==="desc"?W-j:j-W}),a})(),B=a=>{P(!0),v(null),C(a)},h=a=>{k(!0),v(a)},M=async()=>{$(!0),console.log("buddyRequestedFor",r);try{(await q.callRawAPI("/v3/api/custom/courtmatchup/user/buddy/join-team",{buddy_id:r.id,ntrp:A.ntrp,player_ids:[A==null?void 0:A.user_id],num_players:1},"POST")).error||(Z(D,"Request sent successfully",5e3,"success"),P(!1))}catch(a){console.log(a),Z(D,a.message,5e3,"error")}finally{$(!1)}},I=async()=>{c(!0),console.log("buddyRequestedFor",f);try{(await q.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",{buddy_request:ve.court,buddy_id:f.buddy_id,reservation_type:1},"POST")).error||(Z(D,"Reservation created successfully",5e3,"success"),k(!1))}catch(a){console.log(a),Z(D,a.message,5e3,"error")}finally{c(!1)}},H=localStorage.getItem("user");async function F(){try{q.setTable("profile");const a=await q.callRestAPI({id:H},"GET");O(a.model)}catch(a){console.error(a)}}return n.useEffect(()=>{F()},[]),e.jsxs("div",{className:"max mx-auto mt-3 max-w-4xl bg-white p-3 sm:mt-5 sm:p-4",children:[e.jsxs("div",{className:"mb-4 flex flex-col gap-3 sm:mb-6 sm:flex-row sm:items-center sm:justify-between sm:gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"whitespace-nowrap text-sm sm:text-base",children:"Players needed"}),e.jsxs("select",{onChange:a=>L("num_needed",a.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Not set"}),e.jsx("option",{value:"1",children:"1"}),e.jsx("option",{value:"2",children:"2"}),e.jsx("option",{value:"3",children:"3"}),e.jsx("option",{value:"4",children:"4"}),e.jsx("option",{value:"5",children:"5"})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("select",{onChange:a=>L("ntrp",a.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Min NTRP"}),K.map(a=>e.jsx("option",{value:a,children:a},a))]}),e.jsxs("select",{onChange:a=>L("max_ntrp",a.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Max NTRP"}),K.map(a=>e.jsx("option",{value:a,children:a},a))]})]}),e.jsxs("div",{className:"relative sm:pl-2",children:[e.jsxs("button",{onClick:()=>w(!p),className:"flex items-center gap-1 rounded-xl border border-gray-200 bg-white px-2 py-1 text-xs sm:px-4 sm:py-2 sm:text-sm",children:[e.jsxs("span",{className:"whitespace-nowrap text-gray-700",children:["By date (",x==="desc"?"Latest":"Earliest",")"]}),e.jsx(ne,{size:16,className:`text-gray-400 transition-transform duration-200 ${p?"rotate-180":""}`})]}),p&&e.jsxs("div",{className:"absolute right-0 top-full z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white py-1 shadow-lg",children:[e.jsxs("button",{onClick:()=>_("desc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${x==="desc"?"text-blue-600":"text-gray-700"}`,children:["Latest first",x==="desc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),e.jsxs("button",{onClick:()=>_("asc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${x==="asc"?"text-blue-600":"text-gray-700"}`,children:["Earliest first",x==="asc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]})]})]})]})]}),e.jsx("div",{className:"max-h-[500px] space-y-3 overflow-y-auto p-1 sm:space-y-4 sm:p-2",children:T.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 px-4 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No buddies found"})]}):T.map(a=>e.jsx(ie,{clubSports:y,buddy:a,onSelect:()=>v(a)},a.id))}),(f==null?void 0:f.source)==="all-requests"?e.jsx(Ve,{isOpen:f!==null,onClose:()=>v(null),buddy:f,onRequestJoin:B,clubSports:y}):e.jsx(ce,{isOpen:f!==null,onClose:()=>v(null),buddy:f,clubSports:y,fetchData:l,onRequestJoin:B,onReserveCourt:h,setSelectedBuddy:v}),e.jsx(Ze,{isOpen:N,onClose:()=>P(!1),onRequestJoin:M,loading:R}),e.jsx(Te,{isOpen:U,onClose:()=>k(!1),onReserveCourt:I,loading:E})]})}function Ye({fetchData:l,buddyData:d,allBuddyData:s,clubSports:y}){const[x,u]=n.useState(new Date),[p,w]=n.useState(null),[f,v]=n.useState("desc"),[N,P]=n.useState(!1),{club:R}=re(),[$,D]=n.useState({num_needed:null,ntrp:null,max_ntrp:null}),[r,C]=n.useState(null),[A,O]=n.useState(d),U=()=>{let m=A.filter(T=>{let B=!0;return Object.entries($).forEach(([h,M])=>{M!==null&&(B=B&&T[h]===M)}),B});return m=[...m].sort((T,B)=>{const h=new Date(T.date),M=new Date(B.date);return f==="desc"?M-h:h-M}),m},k=(m,T)=>{D(T==="all"?B=>{const h={...B};return delete h[m],h}:B=>({...B,[m]:parseInt(T)}))},E=m=>{v(m),P(!1)},c=async m=>{if(!m)return;const T=new Date(Date.UTC(x.getFullYear(),x.getMonth(),m,0,0,0));w(T);const B=T.toISOString().split("T")[0],h=await l(1,10,[`date,cs,${B}`]);h&&O(h)},b=async()=>{w(null),O(d),await l(1,10,[])},S=()=>{u(new Date(x.getFullYear(),x.getMonth()-1,1))},L=()=>{u(new Date(x.getFullYear(),x.getMonth()+1,1))};n.useEffect(()=>{O(d)},[d]);const _=U();return e.jsx("div",{children:e.jsxs("div",{className:"mx-auto max-w-6xl p-2 sm:p-4",children:[e.jsxs("div",{className:"flex flex-col gap-4 sm:gap-6 md:flex-row md:gap-8",children:[e.jsxs("div",{className:"h-fit w-full rounded-lg bg-white p-3 shadow-sm sm:p-4 sm:shadow-5 md:w-[350px] lg:w-[400px]",children:[e.jsx(Me,{buddies:d,currentMonth:x,selectedDate:p,onDateClick:c,onPreviousMonth:S,onNextMonth:L,onDateSelect:m=>{m&&(w(m),c(m.getDate()))},daysOff:(()=>{try{return R!=null&&R.days_off?JSON.parse(R.days_off):[]}catch(m){return console.error("Error parsing days_off:",m),[]}})()}),p&&e.jsxs("div",{className:"mt-3 flex flex-wrap items-center justify-between border-t border-gray-200 pt-3 sm:mt-4 sm:pt-4",children:[e.jsxs("span",{className:"mr-2 text-xs text-gray-600 sm:text-sm",children:["Showing buddies for"," ",p.toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})]}),e.jsx("button",{onClick:b,className:"text-xs text-blue-600 hover:underline sm:text-sm",children:"Clear"})]})]}),e.jsx("div",{className:"w-full",children:e.jsxs("div",{className:"space-y-3 rounded-lg bg-white p-3 text-xs shadow-sm sm:space-y-4 sm:p-5 sm:text-sm",children:[e.jsxs("div",{className:"mb-4 flex flex-col gap-3 sm:mb-6 sm:flex-row sm:items-center sm:justify-between sm:gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"whitespace-nowrap text-sm",children:"Players needed"}),e.jsxs("select",{onChange:m=>k("num_needed",m.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Not set"}),e.jsx("option",{value:"1",children:"1"}),e.jsx("option",{value:"2",children:"2"}),e.jsx("option",{value:"3",children:"3"}),e.jsx("option",{value:"4",children:"4"}),e.jsx("option",{value:"5",children:"5"})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("select",{onChange:m=>k("ntrp",m.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Min NTRP"}),K.map(m=>e.jsx("option",{value:m,children:m},m))]}),e.jsxs("select",{onChange:m=>k("max_ntrp",m.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Max NTRP"}),K.map(m=>e.jsx("option",{value:m,children:m},m))]})]}),e.jsxs("div",{className:"relative sm:pl-2",children:[e.jsxs("button",{onClick:()=>P(!N),className:"flex items-center gap-1 rounded-xl border border-gray-200 bg-white px-2 py-1 text-xs sm:px-4 sm:py-2 sm:text-sm",children:[e.jsxs("span",{className:"whitespace-nowrap text-gray-700",children:["By date (",f==="desc"?"Latest":"Earliest",")"]}),e.jsx(ne,{size:16,className:`text-gray-400 transition-transform duration-200 ${N?"rotate-180":""}`})]}),N&&e.jsxs("div",{className:"absolute right-0 top-full z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white py-1 shadow-lg",children:[e.jsxs("button",{onClick:()=>E("desc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${f==="desc"?"text-blue-600":"text-gray-700"}`,children:["Latest first",f==="desc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),e.jsxs("button",{onClick:()=>E("asc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${f==="asc"?"text-blue-600":"text-gray-700"}`,children:["Earliest first",f==="asc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]})]})]})]})]}),e.jsx("div",{className:"max-h-[400px] space-y-3 overflow-y-auto p-1 sm:max-h-[500px] sm:space-y-4 sm:p-2",children:_.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 px-4 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No buddies found"})]}):_.map(m=>e.jsx(ie,{clubSports:y,buddy:m,onSelect:()=>C(m)},m.id))})]})})]}),e.jsx(ce,{isOpen:r!==null,onClose:()=>C(null),setSelectedBuddy:C,clubSports:y,buddy:r,fetchData:l})]})})}function Ge({fetchData:l,buddyData:d,clubSports:s}){const[y,x]=n.useState(De()),[u,p]=n.useState(0),[w,f]=n.useState("desc"),[v,N]=n.useState(!1),[P,R]=n.useState({num_needed:null,ntrp:null,max_ntrp:null}),$=()=>{let c=d.filter(b=>{let S=!0;return Object.entries(P).forEach(([L,_])=>{_!==null&&(S=S&&b[L]===_)}),S});return c=[...c].sort((b,S)=>{const L=new Date(b.date),_=new Date(S.date);return w==="desc"?_-L:L-_}),c},D=(c,b)=>{R(b==="all"?S=>{const L={...S};return delete L[c],L}:S=>({...S,[c]:parseInt(b)}))},r=c=>{f(c),N(!1)},C=async()=>{if(u>0){const c=u-1;p(c),x(b=>b.clone().subtract(1,"week")),await l(1,10,{week:c})}},A=async()=>{const c=u+1;p(c),x(b=>b.clone().add(1,"week")),await l(1,10,{week:c})},O=()=>{const c=y.clone().startOf("week"),b=y.clone().endOf("week"),S=`${c.format("MMM D")} - ${b.format("MMM D")}`;return u===0?`This week (${S})`:u===1?`Next week (${S})`:`${u} weeks from now (${S})`},[U,k]=n.useState(null);n.useEffect(()=>{l(1,10,{week:0})},[]);const E=$();return e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"mx-auto mb-3 mt-3 max-w-xs rounded-xl bg-white p-1 shadow-sm sm:mb-5 sm:mt-5 sm:max-w-sm",children:e.jsxs("div",{className:"flex items-center justify-between gap-2 rounded-xl bg-gray-50 p-2 sm:gap-4",children:[e.jsx("button",{onClick:C,disabled:u===0,className:`rounded-xl bg-white p-1 text-gray-600 sm:p-2 ${u===0?"cursor-not-allowed opacity-50":"hover:text-gray-800"}`,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-6 sm:w-6",children:e.jsx("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("div",{className:"text-center text-sm font-medium sm:text-lg",children:O()}),e.jsx("button",{onClick:A,className:"rounded-xl bg-white p-1 text-gray-600 hover:text-gray-800 sm:p-2",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-6 sm:w-6",children:e.jsx("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})}),e.jsxs("div",{className:"mx-auto max-w-4xl space-y-3 rounded-lg bg-white p-3 shadow-sm sm:space-y-4 sm:p-4",children:[e.jsxs("div",{className:"mb-4 flex flex-col gap-3 sm:mb-6 sm:flex-row sm:items-center sm:justify-between sm:gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"whitespace-nowrap text-sm",children:"Players needed"}),e.jsxs("select",{onChange:c=>D("num_needed",c.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Not set"}),e.jsx("option",{value:"1",children:"1"}),e.jsx("option",{value:"2",children:"2"}),e.jsx("option",{value:"3",children:"3"}),e.jsx("option",{value:"4",children:"4"}),e.jsx("option",{value:"5",children:"5"})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("select",{onChange:c=>D("ntrp",c.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Min NTRP"}),K.map(c=>e.jsx("option",{value:c,children:c},c))]}),e.jsxs("select",{onChange:c=>D("max_ntrp",c.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Max NTRP"}),K.map(c=>e.jsx("option",{value:c,children:c},c))]})]}),e.jsxs("div",{className:"relative sm:pl-2",children:[e.jsxs("button",{onClick:()=>N(!v),className:"flex items-center gap-1 rounded-xl border border-gray-200 bg-white px-2 py-1 text-xs sm:px-4 sm:py-2 sm:text-sm",children:[e.jsxs("span",{className:"whitespace-nowrap text-gray-700",children:["By date (",w==="desc"?"Latest":"Earliest",")"]}),e.jsx(ne,{size:16,className:`text-gray-400 transition-transform duration-200 ${v?"rotate-180":""}`})]}),v&&e.jsxs("div",{className:"absolute right-0 top-full z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white py-1 shadow-lg",children:[e.jsxs("button",{onClick:()=>r("desc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${w==="desc"?"text-blue-600":"text-gray-700"}`,children:["Latest first",w==="desc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),e.jsxs("button",{onClick:()=>r("asc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${w==="asc"?"text-blue-600":"text-gray-700"}`,children:["Earliest first",w==="asc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]})]})]})]})]}),E.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 px-4 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No buddies found"})]}):e.jsx("div",{className:"max-h-[400px] space-y-3 overflow-y-auto p-1 sm:max-h-[500px] sm:space-y-4 sm:p-2",children:E.map(c=>e.jsx(ie,{clubSports:s,buddy:c,onSelect:()=>k(c)},c.id))})]}),e.jsx(ce,{isOpen:U!==null,onClose:()=>k(null),buddy:U,clubSports:s,fetchData:l,setSelectedBuddy:k})]})}let ee=new X;const Ke=Ee().shape({sport:J().required("Sport is required"),type:J().when("sport",{is:l=>l,then:()=>J().required("Type is required")}),subtype:J().when(["sport","type"],{is:(l,d)=>l&&d,then:()=>J().required("Subtype is required")}),days:Pe().min(1,"Select at least one day"),dateFrom:J().required("Start date is required"),dateTo:J().required("End date is required").test("date","End date must be after start date",function(l){const{dateFrom:d}=this.parent;return!d||!l?!0:new Date(l)>new Date(d)}),startTime:J().required("Start time is required"),endTime:J().required("End time is required").test("time","End time must be after start time",function(l){const{startTime:d}=this.parent;return!d||!l?!0:l>d}),ntrpMin:de().required("Minimum NTRP is required").min(1,"Minimum NTRP must be at least 1").max(7,"Maximum NTRP must be at most 7"),ntrpMax:de().required("Maximum NTRP is required").min(1,"Minimum NTRP must be at least 1").max(7,"Maximum NTRP must be at most 7").test("ntrp","Maximum NTRP must be greater than minimum",function(l){const{ntrpMin:d}=this.parent;return!d||!l?!0:l>d}),notes:J()});function Qe({isOpen:l,onClose:d,clubProfile:s,userProfile:y,clubSports:x}){const[u,p]=n.useState("list"),[w,f]=n.useState(!1),{dispatch:v}=n.useContext(Q),{control:N,handleSubmit:P,watch:R,setValue:$,reset:D,formState:{errors:r}}=Be({defaultValues:{sport:"",type:"",subtype:"",days:[],dateFrom:"",dateTo:"",ntrpMin:"2",ntrpMax:"5",startTime:"",endTime:"",notes:""},resolver:$e(Ke)}),C=R("sport"),A=R("type"),[O,U]=n.useState([]),[k,E]=n.useState(null),[c,b]=n.useState(null),[S,L]=n.useState(!1);n.useRef(null);const[_]=n.useState(()=>new Map),m=Array.from({length:24},(a,t)=>{const o=t,j=o<12?"AM":"PM";return{label:`${o===0?12:o>12?o-12:o}:00 ${j}`,value:`${o.toString().padStart(2,"0")}:00`}});n.useEffect(()=>{const a=t=>{if(c!==null){const o=_.get(c);o&&!o.contains(t.target)&&b(null)}};return document.addEventListener("mousedown",a),()=>{document.removeEventListener("mousedown",a)}},[c]);async function T(){try{const a=await ee.callRawAPI("/v3/api/custom/courtmatchup/user/buddy/get-subscriptions",{},"GET");U(a.subscriptions)}catch(a){console.error("Error fetching subscriptions:",a)}}n.useEffect(()=>{T()},[]);const B=a=>{E(a),b(null);const t=o=>{const[j]=o.split(":");return`${j.padStart(2,"0")}:00`};D({sport:a.sport_id.toString(),type:a.type,subtype:a.sub_type,days:JSON.parse(a.days),dateFrom:a.start_date,dateTo:a.end_date,startTime:t(a.start_time),endTime:t(a.end_time),ntrpMin:parseInt(a.min_ntrp),ntrpMax:parseInt(a.max_ntrp),notes:a.notes||""}),p("add")},h=()=>{E(null),D({sport:"",type:"",subtype:"",days:[],dateFrom:"",dateTo:"",ntrpMin:"2",ntrpMax:"5",startTime:"",endTime:"",notes:""}),p("list")},M=async a=>{L(!0);try{await ee.callRawAPI(`/v3/api/custom/courtmatchup/user/subscribe/delete/${a}`,{},"GET"),Z(v,"Subscription deleted successfully",3e3,"success"),T()}catch(t){Z(v,t.message||"Error deleting subscription",3e3,"error")}finally{L(!1),b(null)}},I=()=>{var o,j,W,G,oe,me;const a=(i,g)=>{const z=g.value||[],ae=z.includes(i)?z.filter(ue=>ue!==i):[...z,i];g.onChange(ae)},t=async i=>{f(!0);try{const g={sport_id:parseInt(i.sport),min_ntrp:parseFloat(i.ntrpMin),max_ntrp:parseFloat(i.ntrpMax),days:i.days,type:i.type,need_coach:1,notes:i.notes,start_date:i.dateFrom,end_date:i.dateTo,start_time:i.startTime,end_time:i.endTime,sub_type:i.subtype};k?await ee.callRawAPI(`/v3/api/custom/courtmatchup/user/subscribe/edit/${k.id}`,g,"POST"):await ee.callRawAPI("/v3/api/custom/courtmatchup/user/buddy/subscribe",g,"POST"),Z(v,`Subscription ${k?"updated":"added"} successfully`,3e3,"success"),E(null),D({sport:"",type:"",subtype:"",days:[],dateFrom:"",dateTo:"",ntrpMin:"2",ntrpMax:"5",startTime:"",endTime:"",notes:""}),T(),p("list")}catch(g){Z(v,g.message,3e3,"error"),console.error("Error saving subscription:",g)}finally{f(!1)}};return e.jsxs("div",{className:"flex flex-col",children:[e.jsx("div",{className:"mb-4 flex items-center justify-between bg-gray-100 px-5 py-1.5",children:e.jsx("p",{className:"text-sm text-gray-500",children:"DETAILS"})}),e.jsxs("form",{id:"subscription-form",onSubmit:P(t),className:"space-y-6 divide-y divide-gray-200 px-5 pb-20",children:[e.jsxs("div",{className:"space-y-6 pt-4",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Sport"}),r.sport&&e.jsx("p",{className:"text-sm text-red-500",children:r.sport.message}),e.jsx("div",{className:"flex flex-wrap items-center gap-8",children:x.filter(i=>i.status===1).map(i=>e.jsxs("label",{className:"flex items-center",children:[e.jsx(V,{name:"sport",control:N,render:({field:g})=>e.jsx("input",{type:"radio",...g,value:i.id,checked:g.value==i.id,onChange:z=>{g.onChange(z.target.value),$("type",""),$("subtype","")},className:"h-4 w-4 text-blue-600"})}),e.jsx("span",{className:"ml-2 text-gray-900",children:i.name})]},i.id))})]}),C&&e.jsxs("div",{className:"space-y-4",children:[r.type&&e.jsx("p",{className:"text-sm text-red-500",children:r.type.message}),((o=x.find(i=>i.id==C))==null?void 0:o.sport_types.find(i=>i.type))&&e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Type"}),e.jsx("div",{className:"flex flex-wrap items-center gap-8",children:(j=x.find(i=>i.id==C))==null?void 0:j.sport_types.filter(i=>i.type).map((i,g)=>e.jsxs("label",{className:"flex items-center",children:[e.jsx(V,{name:"type",control:N,render:({field:z})=>e.jsx("input",{type:"radio",...z,value:i.type,checked:z.value===i.type,onChange:ae=>{z.onChange(ae.target.value),$("subtype","")},className:"h-4 w-4 text-blue-600"})}),e.jsx("span",{className:"ml-2 text-gray-900",children:i.type})]},g))})]}),C&&A&&e.jsxs("div",{className:"space-y-4",children:[r.subtype&&e.jsx("p",{className:"text-sm text-red-500",children:r.subtype.message}),((G=(W=x.find(i=>i.id==C))==null?void 0:W.sport_types.find(i=>i.type===A))==null?void 0:G.subtype)&&e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Sub-type"}),e.jsx("div",{className:"flex flex-wrap items-center gap-8",children:(me=(oe=x.find(i=>i.id==C))==null?void 0:oe.sport_types.find(i=>i.type==A))==null?void 0:me.subtype.map((i,g)=>e.jsxs("label",{className:"flex items-center",children:[e.jsx(V,{name:"subtype",control:N,render:({field:z})=>e.jsx("input",{type:"radio",...z,value:i,checked:z.value===i,className:"h-4 w-4 text-blue-600"})}),e.jsx("span",{className:"ml-2 text-gray-900",children:i.charAt(0).toUpperCase()+i.slice(1)})]},g))})]})]}),e.jsxs("div",{className:"space-y-4 py-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Days of the week"}),r.days&&e.jsx("p",{className:"text-sm text-red-500",children:r.days.message}),e.jsx("div",{className:"flex flex-wrap gap-3",children:["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"].map(i=>e.jsxs("label",{className:"flex items-center",children:[e.jsx(V,{name:"days",control:N,render:({field:g})=>{var z;return e.jsx("input",{type:"checkbox",checked:((z=g.value)==null?void 0:z.includes(i))||!1,onChange:()=>a(i,g),className:"h-4 w-4 rounded border border-gray-300 text-blue-600"})}}),e.jsx("span",{className:"ml-2 text-gray-900",children:i})]},i))})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Date range"}),r.dateFrom&&e.jsx("p",{className:"text-sm text-red-500",children:r.dateFrom.message}),r.dateTo&&e.jsx("p",{className:"text-sm text-red-500",children:r.dateTo.message}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"relative w-1/2",children:[e.jsx(V,{name:"dateFrom",control:N,render:({field:i})=>e.jsxs(e.Fragment,{children:[e.jsx("input",{type:"date",...i,className:`w-full rounded-xl border ${r.dateFrom?"border-red-500":"border-gray-200"} bg-gray-50 px-4 py-2.5`}),r.dateFrom&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:r.dateFrom.message})]})}),e.jsx("span",{className:"absolute -top-2 left-3 bg-gray-50 px-1 text-xs text-gray-500",children:"From"})]}),e.jsxs("div",{className:"relative w-1/2",children:[e.jsx(V,{name:"dateTo",control:N,render:({field:i})=>e.jsxs(e.Fragment,{children:[e.jsx("input",{type:"date",...i,className:`w-full rounded-xl border ${r.dateTo?"border-red-500":"border-gray-200"} bg-gray-50 px-4 py-2.5`}),r.dateTo&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:r.dateTo.message})]})}),e.jsx("span",{className:"absolute -top-2 left-3 bg-gray-50 px-1 text-xs text-gray-500",children:"To"})]})]})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Time range"}),r.startTime&&e.jsx("p",{className:"text-sm text-red-500",children:r.startTime.message}),r.endTime&&e.jsx("p",{className:"text-sm text-red-500",children:r.endTime.message}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"relative w-1/2",children:[e.jsx(V,{name:"startTime",control:N,render:({field:i})=>e.jsxs(e.Fragment,{children:[e.jsxs("select",{...i,className:`w-full rounded-xl border ${r.startTime?"border-red-500":"border-gray-200"} appearance-none bg-gray-50 px-4 py-2.5`,children:[e.jsx("option",{value:"",children:"Select time"}),m.map(g=>e.jsx("option",{value:g.value,children:g.label},g.value))]}),r.startTime&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:r.startTime.message})]})}),e.jsx("span",{className:"absolute -top-2 left-3 bg-gray-50 px-1 text-xs text-gray-500",children:"Start"})]}),e.jsxs("div",{className:"relative w-1/2",children:[e.jsx(V,{name:"endTime",control:N,render:({field:i})=>e.jsxs(e.Fragment,{children:[e.jsxs("select",{...i,className:`w-full rounded-xl border ${r.endTime?"border-red-500":"border-gray-200"} appearance-none bg-gray-50 px-4 py-2.5`,children:[e.jsx("option",{value:"",children:"Select time"}),m.map(g=>e.jsx("option",{value:g.value,children:g.label},g.value))]}),r.endTime&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:r.endTime.message})]})}),e.jsx("span",{className:"absolute -top-2 left-3 bg-gray-50 px-1 text-xs text-gray-500",children:"End"})]})]})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"NTRP score"}),r.ntrpMin&&e.jsx("p",{className:"text-sm text-red-500",children:r.ntrpMin.message}),r.ntrpMax&&e.jsx("p",{className:"text-sm text-red-500",children:r.ntrpMax.message}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"relative w-1/2",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400",children:"Min"}),e.jsx(V,{name:"ntrpMin",control:N,render:({field:i})=>e.jsxs(e.Fragment,{children:[e.jsx("select",{...i,className:`w-full rounded-xl border ${r.ntrpMin?"border-red-500":"border-gray-200"} appearance-none bg-gray-50 px-4 py-2.5 pl-12`,children:K.map(g=>e.jsx("option",{value:g,children:g.toFixed(1)},g))}),r.ntrpMin&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:r.ntrpMin.message})]})})]}),e.jsxs("div",{className:"relative w-1/2",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400",children:"Max"}),e.jsx(V,{name:"ntrpMax",control:N,render:({field:i})=>e.jsxs(e.Fragment,{children:[e.jsx("select",{...i,className:`w-full rounded-xl border ${r.ntrpMax?"border-red-500":"border-gray-200"} appearance-none bg-gray-50 px-4 py-2.5 pl-12`,children:K.map(g=>e.jsx("option",{value:g,children:g.toFixed(1)},g))}),r.ntrpMax&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:r.ntrpMax.message})]})})]})]})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Notes"}),r.notes&&e.jsx("p",{className:"text-sm text-red-500",children:r.notes.message}),e.jsx(V,{name:"notes",control:N,render:({field:i})=>e.jsx("textarea",{...i,className:`w-full rounded-xl border ${r.notes?"border-red-500":"border-gray-200"} bg-gray-50 px-4 py-2.5`})})]})]}),e.jsxs("div",{className:"fixed bottom-0 flex w-full flex-shrink-0 justify-end gap-4 border-t border-gray-200 bg-white px-4 py-4",children:[e.jsx("button",{type:"button",className:"flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:h,children:"Cancel"}),e.jsxs(se,{type:"submit",form:"subscription-form",className:"flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",onClick:P(t),loading:w,children:[k?"Update":"Save"," changes"]})]})]})},H=({subscription:a,index:t})=>{var j;const o=n.useRef(null);return n.useEffect(()=>(_.set(a.id,o.current),()=>_.delete(a.id)),[a.id]),e.jsxs("div",{className:"mb-4 rounded-2xl border border-gray-200 bg-gray-100 p-3 pt-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("h3",{className:"text-lg font-medium",children:["Subscription #",t+1]}),e.jsxs("div",{className:"relative",ref:o,children:[e.jsx("button",{onClick:()=>b(c===a.id?null:a.id),className:"rounded-full p-1 text-gray-500 hover:bg-gray-200 hover:text-gray-700",children:e.jsx(Le,{size:20})}),c===a.id&&e.jsx("div",{className:"absolute right-0 z-50 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsxs("div",{className:"py-1",role:"menu","aria-orientation":"vertical",children:[e.jsx("button",{onClick:()=>B(a),className:"w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100",role:"menuitem",children:"Edit"}),e.jsx("button",{onClick:()=>M(a.id),disabled:S,className:"w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100",role:"menuitem",children:S?"Deleting...":"Delete"})]})})]})]}),e.jsxs("div",{className:"mt-4 space-y-2 rounded-xl bg-white p-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Sport"}),e.jsxs("span",{className:"font-medium",children:[a==null?void 0:a.sport_name," ",(a==null?void 0:a.type)&&`• ${a==null?void 0:a.type}`," ",(a==null?void 0:a.sub_type)&&`• ${a==null?void 0:a.sub_type}`]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Date & time"}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-medium",children:_e(new Date(a==null?void 0:a.start_date),"MMM dd, yyyy")}),e.jsxs("div",{className:"font-medium",children:[Y(a==null?void 0:a.start_time)," -"," ",Y(a==null?void 0:a.end_time)]})]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Days of the week"}),e.jsx("div",{className:"text-right",children:(j=JSON.parse(a==null?void 0:a.days))==null?void 0:j.map((W,G)=>e.jsx("div",{className:"font-medium",children:W},G))})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Min - max NTRP"}),e.jsxs("span",{className:"font-medium",children:[a==null?void 0:a.min_ntrp," - ",a==null?void 0:a.max_ntrp]})]})]})]})},F=()=>e.jsxs("div",{className:"flex flex-col",children:[e.jsx("div",{className:"bg-gray-100 px-5 py-1.5 ",children:e.jsx("p",{className:"text-sm text-gray-500",children:"REQUESTED BY"})}),e.jsx("div",{className:" p-5",children:e.jsx("p",{className:"text-gray-600",children:"Enter date/time range below and hit confirm. When Find-a-buddy request is made that fits into this range, you will be notified via email, and can go online to join the request if you like."})}),e.jsxs("div",{className:"mb-4 flex items-center justify-between bg-gray-100 px-5 py-1.5 ",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"DATES/TIMES ADDED"}),e.jsxs("button",{onClick:()=>p("add"),className:"flex items-center border-b border-blue-600 text-sm text-blue-600",children:[e.jsx(Ne,{className:"mr-1"}),O.length===0?"Add subscription":"Add another"]})]}),e.jsxs("div",{className:"px-5",children:[O.map((a,t)=>e.jsx(H,{subscription:a,index:t},a.id)),O.length===0&&e.jsx("div",{className:"flex items-center justify-center p-5",children:e.jsx("p",{className:"text-gray-500",children:"Subscriptions is empty"})})]})]});return e.jsx(le,{isOpen:l,onClose:d,title:u==="list"?"Subscriptions":k?"Edit subscription":"Add subscription",showFooter:!1,className:"!p-0",children:u==="list"?e.jsx(F,{}):e.jsx(I,{})})}let Xe=new Ce,pe=new X;function Hs(){const[l,d]=n.useState(null),[s,y]=n.useState(!0),{dispatch:x}=n.useContext(Q),{dispatch:u}=n.useContext(te),{user_permissions:p}=re(),[w,f]=n.useState(!1),[v,N]=n.useState("all-requests"),[P,R]=n.useState([]),[$,D]=n.useState([]),[r,C]=n.useState(!1),[A,O]=n.useState(null),[U,k]=n.useState(null),[E,c]=n.useState([]),b=[{id:"table",label:"Table"},{id:"calendar",label:"Calendar"},{id:"weekly",label:"Weekly"}],S=[{id:"all-requests",label:"All requests"},{id:"my-requests",label:"My requests"}],L=localStorage.getItem("user"),_=async(h,M,I={},H="all-requests")=>{f(!0),console.log("filters",I);try{let F=H==="all-requests"?"/v3/api/custom/courtmatchup/user/buddy/all-requests":"/v3/api/custom/courtmatchup/user/buddy/my-requests";const a=[];I&&typeof I=="object"&&Object.entries(I).forEach(([o,j])=>{j&&a.push(`${o}=${encodeURIComponent(j)}`)}),a.length>0&&(F+=`?${a.join("&")}`);const t=await pe.callRawAPI(F,{},"GET");if(!t.error){f(!1);const o=H==="all-requests"?t.list.map(j=>({...j,source:"all-requests"})):t.my_requests.map(j=>({...j,source:"my-requests"}));return R(o),o}}catch(F){f(!1),console.log("ERROR",F),xe(u,F.message)}};async function m(){var h;try{const M=await Xe.getOne("user",L,{}),I=await pe.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${M.model.club_id}`,{},"GET");O(I.model),k(M.model),c(I.sports);let H="table";if((h=I.model)!=null&&h.buddy_description)try{const F=JSON.parse(I.model.buddy_description);F.default_view&&(H=F.default_view)}catch(F){console.error("Error parsing buddy_description:",F)}d(H),y(!1)}catch(M){console.log("ERROR",M),d("table"),y(!1)}}async function T(h={}){try{const M=await _(1,10,h);D(M)}catch(M){console.log("ERROR",M),xe(u,M.message)}}n.useEffect(()=>{m(),x({type:"SETPATH",payload:{path:"find-a-buddy"}})},[]),n.useEffect(()=>{l&&(_(1,10,{}),T({}))},[l]);const B=h=>{N(h),_(1,10,{},h)};return p&&!p.allowBuddy?e.jsx(Oe,{message:`Your current plan (${p==null?void 0:p.planName}) does not include find a buddy feature. Please upgrade your plan to access this feature.`}):e.jsxs(e.Fragment,{children:[(w||!l)&&e.jsx(ye,{}),l&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white px-3 sm:px-4",children:[e.jsxs("div",{className:"flex flex-col pt-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h1",{className:"mb-4 text-xl font-semibold sm:mb-6 sm:text-2xl",children:"Find a buddy"}),e.jsxs("div",{className:"mb-4 flex flex-wrap items-center gap-2 sm:mb-0",children:[e.jsx("button",{onClick:()=>C(!0),className:"rounded-xl border bg-green-900 px-3 py-2 text-xs text-white sm:px-4 sm:text-sm",children:"Subscriptions"}),e.jsxs(fe,{to:"/user/create-request",className:"flex items-center rounded-xl border bg-primaryBlue px-3 py-2 text-xs text-white sm:text-sm",children:[e.jsx("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.25 5.25V0.75H6.75V5.25H11.25V6.75H6.75V11.25H5.25V6.75H0.75V5.25H5.25Z",fill:"white"})}),e.jsx("span",{className:"ml-2",children:"Create a request"})]})]})]}),e.jsxs("div",{className:"flex flex-col pt-2 sm:flex-row sm:items-center sm:justify-between sm:pt-4",children:[e.jsx("div",{className:"mb-3 flex max-w-fit overflow-x-auto text-xs sm:mb-0 sm:text-sm",children:S.map(h=>e.jsx("button",{onClick:()=>B(h.id),className:`flex items-center gap-2 whitespace-nowrap bg-transparent px-3 py-2 sm:py-3 ${v===h.id?"border-b-2 border-primaryBlue":""}`,children:e.jsx("span",{children:h.label})},h.id))}),e.jsx("div",{className:"mb-3 flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:text-sm",children:b.map(h=>e.jsx("button",{onClick:()=>d(h.id),className:`whitespace-nowrap px-2 py-2 sm:px-3 ${l===h.id?"bg-white-600":"bg-gray-100 text-gray-600"}`,children:h.label},h.id))})]})]}),e.jsx("div",{className:"px-2 py-2 sm:px-3 sm:py-3",children:e.jsxs("div",{className:"mx-auto max-w-7xl",children:[l==="table"&&e.jsx(Je,{fetchData:_,buddyData:P,setBuddyData:R,clubSports:E}),l==="calendar"&&e.jsx(Ye,{fetchData:_,buddyData:P,setBuddyData:R,allBuddyData:$,clubSports:E}),l==="weekly"&&e.jsx(Ge,{fetchData:_,buddyData:P,setBuddyData:R,clubSports:E})]})}),e.jsx(Qe,{isOpen:r,onClose:()=>C(!1),clubProfile:A,userProfile:U,clubSports:E})]})]})}export{Hs as default};
