import{j as a}from"./@nivo/heatmap-ba1ecfff.js";import{b as t,f as G}from"./vendor-851db8c1.js";import{M as K,T as U,G as V,A as I,c as Y,H as p,t as J}from"./index-ca7cbd3e.js";import"./index-be4468eb.js";import{c as Q,a as c}from"./yup-54691517.js";import{u as W}from"./react-hook-form-687afde5.js";import{o as X}from"./yup-2824f222.js";import"./AddButton.module-98aac587.js";import{P as Z}from"./index-eb1bc208.js";import{_ as ee}from"./lodash-91d5d207.js";import te from"./Skeleton-1e8bf077.js";import{D as ae}from"./DataTable-a2248415.js";new K;let se=new U;const oe=[{header:"Date",accessor:"create_at"},{header:"Time",accessor:"update_at"},{header:"Staff name",accessor:"user"},{header:"Action",accessor:"description"}],Ne=({club:r,sports:re})=>{const{dispatch:S,state:ne}=t.useContext(V),{dispatch:v}=t.useContext(I),[j,C]=t.useState([]),[o,g]=t.useState(10),[N,_]=t.useState(0),[ie,P]=t.useState(0),[d,E]=t.useState(0),[b,T]=t.useState(!1),[D,A]=t.useState(!1),[le,L]=t.useState(!1);t.useState(!1),t.useState([]),t.useState([]),t.useState("eq");const[f,u]=t.useState(!0),[w,ce]=t.useState(!1),[R,de]=t.useState(!1);t.useState(),G();const x=t.useRef(null),$=localStorage.getItem("user"),k=Q({id:c(),email:c(),role:c(),status:c()});W({resolver:X(k)});function F(){n(d-1,o)}function M(){n(d+1,o)}async function n(e,s,l=[]){console.log("filters",l),u(!(R||w));try{const i=await se.getPaginate("activity_logs",{page:e,limit:s,size:o,filter:[...l,`courtmatchup_activity_logs.club_id,eq,${r==null?void 0:r.id}`],join:["clubs|club_id","user|user_id"]});i&&u(!1);const{list:O,total:q,limit:B,num_pages:y,page:m}=i;C(O),g(B),_(y),E(m),P(q),T(m>1),A(m+1<=y)}catch(i){u(!1),console.log("ERROR",i),J(v,i.message)}}const z=ee.debounce(e=>{const s=e.target.value.trim(),l=s?[`courtmatchup_activity_logs.description,cs,${s}`]:[];s?n(1,o,l):n(1,o)},500);t.useEffect(()=>{S({type:"SETPATH",payload:{path:"history"}}),r!=null&&r.id&&n(1,o)},[r==null?void 0:r.id]);const h=e=>{x.current&&!x.current.contains(e.target)&&L(!1)};t.useEffect(()=>(document.addEventListener("mousedown",h),()=>{document.removeEventListener("mousedown",h)}),[]);const H={update_at:e=>a.jsx("span",{className:"text-gray-600",children:new Date(e.update_at).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),user:e=>{const s=(e==null?void 0:e.user)||{};return a.jsx("span",{className:"text-gray-600",children:(s==null?void 0:s.id)==$?"You":`${(s==null?void 0:s.first_name)||"--"} ${(s==null?void 0:s.last_name)||"--"}`})},action:e=>a.jsx("span",{className:"text-gray-600",children:(e==null?void 0:e.action)||"--"})};return a.jsxs("div",{className:"h-screen px-8",children:[a.jsxs("div",{className:"flex flex-col !justify-between gap-4 py-3 md:flex-row md:items-center",children:[a.jsxs("div",{className:"relative flex max-w-md flex-1 items-center",children:[a.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:a.jsx(Y,{className:"text-gray-500"})}),a.jsx("input",{type:"text",className:"block w-full rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search history",onChange:e=>z(e)})]}),a.jsx("div",{className:"flex items-center gap-4",children:a.jsxs("select",{className:"rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700",defaultValue:"All",onChange:e=>{e.target.value===""?n(1,o):n(1,o,[`courtmatchup_activity_logs.action_type,cs,${e.target.value}`])},children:[a.jsx("option",{value:"",children:"Action type: All"}),a.jsx("option",{value:p.CREATE,children:"Create"}),a.jsx("option",{value:p.UPDATE,children:"Update"}),a.jsx("option",{value:p.DELETE,children:"Delete"})]})})]}),f?a.jsx(te,{}):a.jsx("div",{className:"overflow-x-auto rounded-lg",children:a.jsx(ae,{columns:oe,data:j,loading:f,renderCustomCell:H,tableClassName:"min-w-full border-separate border-spacing-y-2",rowClassName:"bg-gray-100 px-4 py-3",cellClassName:"px-6 py-4",headerClassName:"px-6 py-4 text-left text-sm font-medium text-gray-500",emptyMessage:"No data available",loadingMessage:"Loading..."})}),a.jsx(Z,{currentPage:d,pageCount:N,pageSize:o,canPreviousPage:b,canNextPage:D,updatePageSize:e=>{g(e),n(1,e)},previousPage:F,nextPage:M,gotoPage:e=>n(e,o)})]})};export{Ne as L};
