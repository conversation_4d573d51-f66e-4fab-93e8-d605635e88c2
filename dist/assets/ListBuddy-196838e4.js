import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as r,r as c,f as ve}from"./vendor-851db8c1.js";import{T as R,M as je,G as ye,A as _e,D as Ne,c as Se,U as Ce,J as we,b as T,t as E,a0 as De,ao as ke}from"./index-ca7cbd3e.js";import"./index-be4468eb.js";import"./AddButton.module-98aac587.js";import{P as Pe}from"./index-eb1bc208.js";import{_ as b}from"./lodash-91d5d207.js";import Le from"./Skeleton-1e8bf077.js";import{D as Me}from"./DataTable-a2248415.js";import{H as Te}from"./HistoryComponent-cf25af7a.js";new R;let h=new je,Ee=new R;const be=[{header:"Request created",accessor:"update_at"},{header:"Looking for",accessor:"num_needed"},{header:"Requesting Players",accessor:"num_players"},{header:"Requested by",accessor:"user_id"},{header:"Sport",accessor:"sport_id"},{header:"Court booked",accessor:"court_booked"},{header:"",accessor:"actions"}],Re=({sports:i,club:l,courts:$})=>{const{dispatch:p}=r.useContext(ye),{dispatch:j}=r.useContext(_e),[A,q]=r.useState([]),[o,y]=r.useState(10),[B,F]=r.useState(0),[$e,O]=r.useState(0),[g,H]=r.useState(0),[I,z]=r.useState(!1),[G,V]=r.useState(!1),[Ae,Z]=r.useState(!1),[_,m]=r.useState(!0),[K,qe]=r.useState(!1),[Q,Be]=r.useState(!1),[d,J]=c.useState(null),U=ve(),N=r.useRef(null),[S,W]=r.useState([]),[X,f]=r.useState(!1),[C,Y]=c.useState(""),[ee,te]=c.useState(!1),[se,w]=c.useState(!1),[ae,D]=c.useState(!1),[re,ne]=c.useState(null);function oe(){n(g-1,o)}function le(){n(g+1,o)}const ie=async()=>{try{h.setTable("user");const e=await h.callRestAPI({filter:[`club_id,eq,${l==null?void 0:l.id}`,"role,cs,user"]},"GETALL");W(e.list||[])}catch(e){console.error("Error fetching members:",e),T(p,"Error fetching members",3e3,"error")}};async function n(e,a,s={},u=[]){m(!(Q||K));try{const x=await Ee.getPaginate("buddy",{page:e,limit:a,filter:[...u,`courtmatchup_reservation.club_id,eq,${l==null?void 0:l.id}`],join:["sports|sport_id","user|user_id|player_ids","reservation|reservation_id"]});x&&m(!1);const{list:pe,total:me,limit:fe,num_pages:M,page:v}=x;q(pe),y(fe),F(M),H(v),O(me),z(v>1),V(v+1<=M)}catch(x){m(!1),console.log("ERROR",x),E(j,x.message)}}const ce=e=>{e.target.value===""?n(1,o):n(1,o,{},[`status,cs,${e.target.value}`])};r.useEffect(()=>{p({type:"SETPATH",payload:{path:"find-a-buddy"}}),ie();const a=setTimeout(async()=>{await n(1,o)},700);return()=>{clearTimeout(a)}},[l==null?void 0:l.id]);const k=e=>{N.current&&!N.current.contains(e.target)&&Z(!1)};r.useEffect(()=>(document.addEventListener("mousedown",k),()=>{document.removeEventListener("mousedown",k)}),[]);const de=async e=>{w(!0);try{h.setTable("buddy"),await h.callRestAPI({id:e},"DELETE"),n(g,o)}catch(a){console.error("Error deleting buddy:",a),E(j,a.message)}finally{w(!1)}},ue=e=>{const a=e.target.value;a===""?n(1,o):n(1,o,{},[`sport_id,cs,${a}`])},xe=()=>{const e=S.filter(s=>`${s.first_name||""} ${s.last_name||""}`.toLowerCase().includes(C.toLowerCase())),a=s=>{J(s)};return t.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:t.jsxs("div",{className:"w-96 rounded-lg bg-white shadow-lg",children:[t.jsxs("div",{className:"mb-0 flex items-center justify-between p-4",children:[t.jsx("h3",{className:"text-lg font-medium",children:"New Find Buddy Request"}),t.jsx("button",{onClick:()=>setIsCoachModalOpen(!1),className:"text-gray-400 hover:text-gray-600",children:t.jsx(De,{className:"h-5 w-5"})})]}),t.jsx("h2",{className:"px-4 text-base font-medium",children:"Request on behalf of:"}),t.jsxs("div",{className:"p-4",children:[t.jsx("div",{className:"mb-4",children:t.jsxs("div",{className:"flex  items-center gap-1 rounded-lg border border-gray-300 px-2 focus:border-blue-500 focus:outline-none",children:[t.jsx("span",{className:"w-5",children:t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),t.jsx("input",{type:"text",placeholder:"search by name",value:C,onChange:s=>Y(s.target.value),className:"w-full border-none bg-transparent focus:outline-none focus:ring-0"})]})}),t.jsxs("div",{className:"max-h-64 space-y-2 overflow-y-auto rounded-xl bg-gray-50",children:[e.length>0&&e.map(s=>t.jsxs("div",{onClick:()=>a(s),className:"flex cursor-pointer items-center space-x-3 rounded-lg p-2 hover:bg-gray-50",children:[t.jsx("input",{type:"radio",checked:(d==null?void 0:d.id)===s.id,onChange:()=>a(s),className:"h-4 w-4 rounded-full border-gray-300 text-blue-600"}),t.jsx("div",{className:"h-8 w-8 overflow-hidden rounded-full bg-gray-200",children:t.jsx("img",{src:(s==null?void 0:s.photo)||"/default-avatar.png",alt:`${s.first_name} ${s.last_name}`,className:"h-full w-full object-cover"})}),t.jsx("span",{children:`${s.first_name} ${s.last_name}`})]},s.id)),!(e!=null&&e.length)&&t.jsx("p",{className:"text-center text-sm text-gray-500",children:"No members found"})]}),t.jsxs("div",{className:"mt-4 flex justify-between gap-3 pt-3",children:[t.jsx("button",{onClick:()=>f(!1),className:"flex-1 rounded-xl border border-gray-300 px-4 py-2 text-gray-600 hover:text-gray-800",children:"Cancel"}),t.jsx("button",{onClick:()=>{d?(f(!1),U("/club/add-find_a_buddy_requests",{state:{player:d}})):T(p,"Please select a member",3e3,"error")},className:"flex-1 rounded-xl bg-blue-900 px-4 py-2 text-white hover:bg-blue-800",children:"Save and close"})]})]})]})})},ge=b.debounce(e=>{e?n(1,o,{},[`first_name,cs,${e}`,`last_name,cs,${e}`]):n(1,o)},300),P=b.debounce(e=>{e?n(1,o,{},[`date,cs,${e}`]):n(1,o)},300),L=e=>{const a={...e,id:e==null?void 0:e.id,date:e==null?void 0:e.date,startTime:e==null?void 0:e.start_time,endTime:e==null?void 0:e.end_time,sport_id:e==null?void 0:e.sport_id,type:e==null?void 0:e.type,sub_type:e==null?void 0:e.sub_type,reservation_type:2,price:e==null?void 0:e.price,status:e==null?void 0:e.status,player_ids:e==null?void 0:e.player_ids,coach_ids:e==null?void 0:e.coach_ids};ne(a),D(!0)},he={actions:e=>t.jsx("div",{className:"flex items-center gap-3",children:t.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",onClick:a=>{a.stopPropagation(),L(e)},children:t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})}),user_id:e=>{var a,s,u;return(a=e==null?void 0:e.user)!=null&&a.first_name?`${(s=e==null?void 0:e.user)==null?void 0:s.first_name} ${(u=e==null?void 0:e.user)==null?void 0:u.last_name}`:"--"},sport_id:e=>{var a;return(a=i==null?void 0:i.find(s=>s.id===(e==null?void 0:e.sport_id)))==null?void 0:a.name},players:e=>"2 players",update_at:e=>ke(e==null?void 0:e.update_at),court_booked:e=>e.reservation_id?"Yes":"No"};return t.jsxs("div",{className:"h-screen px-8",children:[t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("div",{children:t.jsx("h3",{className:"text-2xl font-medium leading-6 text-gray-900",children:"Find a buddy"})}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsxs("button",{onClick:()=>f(!0),className:"inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[t.jsx("span",{children:"+"}),"Add new"]}),t.jsx(Te,{title:"Buddy History",emptyMessage:"No buddy history found",activityType:Ne.find_a_buddy})]})]}),t.jsxs("div",{className:"flex flex-col justify-between gap-4 py-3 md:flex-row md:items-center",children:[t.jsxs("div",{className:"relative flex max-w-sm flex-1 items-center",children:[t.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:t.jsx(Se,{className:"text-gray-500"})}),t.jsx("input",{type:"text",onChange:e=>ge(e.target.value),className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search buddy"})]}),t.jsxs("select",{className:"rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700",defaultValue:"All",onChange:ue,children:[t.jsx("option",{value:"",children:"Sport: All"}),i==null?void 0:i.map(e=>t.jsx("option",{value:e.id,children:e.name}))]}),t.jsxs("div",{className:"flex flex-wrap gap-4",children:[t.jsx("input",{type:"date",className:"rounded-md border border-gray-200 text-sm text-gray-500",onChange:e=>P(e.target.value)}),t.jsx("input",{type:"date",className:"rounded-md border border-gray-200 text-sm text-gray-500",onChange:e=>P(e.target.value)}),t.jsx("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700",defaultValue:"All",onChange:ce,children:t.jsx("option",{value:"",children:"Requesting party:?"})})]})]}),_?t.jsx(Le,{}):t.jsx(Me,{columns:be,data:A,loading:_,renderCustomCell:he,rowClassName:"hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer",emptyMessage:"No buddy requests available",loadingMessage:"Loading buddy requests...",onClick:e=>L(e)}),t.jsx(Pe,{currentPage:g,pageCount:B,pageSize:o,canPreviousPage:I,canNextPage:G,updatePageSize:e=>{y(e),n(1,e)},previousPage:oe,nextPage:le,gotoPage:e=>n(e,o)}),X&&t.jsx(xe,{}),t.jsx(Ce,{isOpen:ae,onClose:()=>D(!1),event:re,users:S||[],sports:i||[],club:l,fetchData:n,courts:$||[]}),t.jsx(we,{isOpen:ee,onClose:()=>te(!1),onDelete:de,loading:se,title:"Delete",message:"Are you sure you want to delete this find a buddy request?"})]})},Je=Re;export{Je as L};
