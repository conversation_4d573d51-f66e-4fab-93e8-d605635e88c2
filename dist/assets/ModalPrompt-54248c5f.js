import{j as r}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";import{C as h}from"./index-a73f2a08.js";import{d as c}from"./index-ca7cbd3e.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./cal-heatmap-cf010ec4.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const F=({open:f,closeModalFunction:m,actionHandler:u,message:t,title:e,messageClasses:x="font-normal text-base",titleClasses:b="text-center font-bold text-lg",acceptText:i="YES",rejectText:s="NO",loading:o=!1,isInfo:l=!1})=>{var n,p,a,d;return r.jsx("aside",{className:`fixed inset-0 m-auto flex items-center justify-center backdrop-blur-sm transition-all ${f?"scale-100":"scale-0"}`,children:r.jsxs("section",{className:"flex w-auto min-w-[27rem]  flex-col gap-3 rounded-[.5rem] bg-white py-6",children:[r.jsxs("div",{className:"flex justify-between px-6",children:[r.jsx("div",{children:e?r.jsx("div",{className:` ${b} `,children:e}):null}),r.jsx("button",{disabled:o,onClick:m,children:r.jsx(h,{className:"w-4 h-4"})})]}),t?r.jsxs("div",{children:[r.jsx("div",{className:`px-6 text-[#525252] ${x} `,children:t}),!l&&r.jsx("div",{className:"px-6 text-[#525252] font-normal pt-3 pb-1",children:"This action cannot be undone."})]}):null,r.jsxs("div",{className:"flex justify-between px-6 font-medium leading-[1.5rem] text-[base]",children:[r.jsx("button",{disabled:o,className:"flex h-[2.75rem] items-center justify-center rounded-[.5rem] border border-[#d8dae5] text-[#667085] w-full mr-2",onClick:m,children:((n=s==null?void 0:s.charAt(0))==null?void 0:n.toUpperCase())+((p=s==null?void 0:s.slice(1))==null?void 0:p.toLowerCase())}),r.jsxs(c,{disabled:o,loading:o,className:`flex items-center justify-center gap-x-5 rounded-[.5rem]  ${l?"bg-primaryBlue":"bg-[#E11D48]"} px-6 text-white w-full ml-2`,onClick:u,children:["Yes, ",((a=i==null?void 0:i.charAt(0))==null?void 0:a.toUpperCase())+((d=i==null?void 0:i.slice(1))==null?void 0:d.toLowerCase())]})]})]})})};export{F as default};
