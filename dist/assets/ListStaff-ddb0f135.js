import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as I,b as a,f as Ee,L as re}from"./vendor-851db8c1.js";import{M as le,G as ce,R as $e,E as O,D as w,H as B,b as M,T as we,A as Ae,c as De,J as Ve,t as oe}from"./index-ca7cbd3e.js";import"./index-be4468eb.js";import"./yup-54691517.js";import{a as He}from"./index.esm-9c6194ba.js";import"./AddButton.module-98aac587.js";import{P as Ze}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import Re from"./Skeleton-1e8bf077.js";import"./numeral-ea653b2a.js";import{T as Ie}from"./TimeslotPicker-e509ecbb.js";import{I as Oe}from"./InvitationCard-9a724ad6.js";import{D as Be}from"./DataTable-a2248415.js";import{H as Ue}from"./HistoryComponent-cf25af7a.js";let $=new le;const Fe=[{id:"dashboard",name:"Dashboard"},{id:"daily_scheduler",name:"Daily Scheduler"},{id:"court_management",name:"Court Management"},{id:"availability_dashboard",name:"Availability Dashboard"},{id:"club_ui",name:"Club UI"},{id:"court_reservation",name:"Court Reservation"},{id:"availability_dashboard_all",name:"Availability Dashboard (All Staff)"},{id:"lessons",name:"Lessons"},{id:"clinics",name:"Clinics"},{id:"custom_requests",name:"Custom Requests"},{id:"find_a_buddy",name:"Find-A-Buddy"},{id:"coach",name:"Coach"},{id:"user",name:"User"},{id:"staff",name:"Staff"},{id:"email",name:"Email"},{id:"invoicing",name:"Invoicing"},{id:"invoicing_bank",name:"Invoicing - Club Bank Details"},{id:"history",name:"History"}],j={staff:{dashboard:!0,daily_scheduler:!0,court_management:!0,availability_dashboard:!0,availability_dashboard_all:!0,club_ui:!0,lessons:!0,clinics:!0,custom_requests:!0,find_a_buddy:!0,coach:!0,staff:!1,user:!0,email:!0,invoicing:!0,invoicing_bank:!0,history:!0,court_reservation:!0}};function Je({isOpen:r,onClose:x,roleAccessData:o,club:m}){const[L,P]=I.useState(!1),{dispatch:k,state:g}=a.useContext(ce),[S,C]=I.useState(j),_=localStorage.getItem("role");I.useEffect(()=>{var n;if(o!=null&&o.permission)try{const d=JSON.parse(o.permission),f={};for(const b in j.staff)f[b]=b==="staff"?((n=d.staff)==null?void 0:n.staff)||!1:!0;C({staff:{...j.staff,...f}})}catch(d){console.error("Error parsing permissions:",d),C(j)}else{const d={...j,staff:Object.keys(j.staff).reduce((f,b)=>(f[b]=b!=="staff",f),{})};C(d)}},[o]);const h=n=>{_==="staff"&&n==="staff"||C(d=>{var f;return{...d,staff:{...d.staff,[n]:!((f=d.staff)!=null&&f[n])}}})},A=async()=>{P(!0);try{const n={club_id:m==null?void 0:m.id,permission:JSON.stringify(S)};$.setTable("club_permissions"),o!=null&&o.id?(n.id=o.id,await $.callRestAPI(n,"PUT")):await $.callRestAPI(n,"POST"),await O($,{user_id:localStorage.getItem("user"),activity_type:w.club_ui,action_type:B.UPDATE,data:n,club_id:m==null?void 0:m.id,description:"Updated role access"}),M(k,"Permissions saved successfully",3e3,"success"),x()}catch(n){console.log(n),M(k,"Error saving permissions",3e3,"error")}finally{P(!1)}};return t.jsx($e,{isOpen:r,onClose:x,title:"Role access",onPrimaryAction:A,primaryButtonText:"Save changes",submitting:L,children:t.jsxs("div",{className:"flex flex-col",children:[t.jsxs("div",{className:"mb-4 grid grid-cols-[1fr,auto] items-center gap-4 px-2",children:[t.jsx("div",{className:"text-sm font-medium text-gray-500",children:"Module"}),t.jsx("div",{className:"text-sm font-medium text-gray-500",children:"Staff"})]}),t.jsx("div",{className:"flex flex-col gap-2",children:Fe.map(n=>{var d,f;return t.jsxs("div",{className:"grid grid-cols-[1fr,auto] items-center gap-4 rounded-lg px-2 py-2 hover:bg-gray-50",children:[t.jsx("span",{className:"text-sm text-gray-900",children:n.name}),t.jsx("button",{onClick:()=>!(_==="staff"&&n.id==="staff")&&h(n.id),disabled:_==="staff"&&n.id==="staff",className:`h-6 w-11 rounded-full p-1 transition-colors duration-200 ${(d=S.staff)!=null&&d[n.id]?"bg-blue-600":"bg-gray-200"} ${_==="staff"&&n.id==="staff"?"cursor-not-allowed opacity-50":""}`,children:t.jsx("div",{className:`h-4 w-4 rounded-full bg-white transition-transform duration-200 ${(f=S.staff)!=null&&f[n.id]?"translate-x-5":"translate-x-0"}`})})]},n.id)})})]})})}const qe=r=>{if(!r)return"--";const x=r.replace(/\D/g,"");if(x.length<10)return r;const o=x.match(/^(\d{1})(\d{3})(\d{3})(\d{4})$/);if(o)return`(${o[2]}) ${o[3]}-${o[4]}`;if(x.length===10){const m=x.match(/^(\d{3})(\d{3})(\d{4})$/);return`(${m[1]}) ${m[2]}-${m[3]}`}return r};let y=new le,ze=new we;const Ge=["monday","tuesday","wednesday","thursday","friday","saturday","sunday"],Ke=({club:r,sports:x})=>{var ie,ne;const{dispatch:o,state:m}=a.useContext(ce),{dispatch:L}=a.useContext(Ae),[P,k]=a.useState([]),[g,S]=a.useState(10),[C,_]=a.useState(0),[h,A]=a.useState(1),[n,d]=a.useState(!1),[f,b]=a.useState(!1),[Qe,de]=a.useState(!1),[U,D]=a.useState(!0),[ue,We]=a.useState(!1),[fe,Xe]=a.useState(!1),[F,V]=a.useState(!1),[H,T]=a.useState([]),[l,J]=a.useState(null),[me,q]=a.useState(!1);a.useState([]);const[ge,z]=a.useState(!1),[he,G]=a.useState(!1),[pe,ye]=a.useState(null),[K,xe]=a.useState(!1);a.useState(null);const[ve,Q]=a.useState(!1),[W,X]=a.useState(""),[Y,Ce]=a.useState(null),[be,ee]=a.useState(!1),[Se,Ye]=a.useState([]),Z=localStorage.getItem("role"),te=localStorage.getItem("user"),_e=Ee(),se=a.useRef(null);function je(){p(h-1,g)}function Me(){p(h+1,g)}async function p(e,s,i={},u=[]){D(!(fe||ue));try{const c=await ze.getPaginate("staff",{page:e,limit:s,filter:[...u,`courtmatchup_staff.club_id,eq,${r==null?void 0:r.id}`],join:["user|user_id"]});c&&D(!1);const{list:v,limit:E,num_pages:N,page:R}=c;k(v),S(E),_(N),A(R),d(R>1),b(R+1<=N)}catch(c){D(!1),console.log("ERROR",c),oe(L,c.message)}}a.useEffect(()=>{o({type:"SETPATH",payload:{path:"staff"}});const s=setTimeout(async()=>{await p(h,g,{})},700);return()=>{clearTimeout(s)}},[r==null?void 0:r.id]);const ae=e=>{se.current&&!se.current.contains(e.target)&&de(!1)};a.useEffect(()=>(document.addEventListener("mousedown",ae),()=>{document.removeEventListener("mousedown",ae)}),[]);const Le=async({staff_id:e,user_id:s})=>{G(!0);try{y.setTable("user"),await y.callRestAPI({id:s},"DELETE"),y.setTable("staff"),await y.callRestAPI({id:e},"DELETE"),await O(y,{user_id:te,activity_type:w.staff_management,action_type:B.DELETE,data:{staff_id:e,staff_user_id:s},club_id:r==null?void 0:r.id,description:"Deleted staff"}),M(o,"Staff deleted successfully",3e3,"success"),p(h,g)}catch(i){console.error("Error deleting staff:",i),M(o,i.message,3e3,"error"),oe(L,i.message)}G(!1)},Ne=e=>{if(J(e),V(!0),e.availability)try{const s=JSON.parse(e.availability),i=Ge.map(u=>({day:u.toLowerCase(),timeslots:[]}));s&&Array.isArray(s)&&s.length>0&&s.forEach(u=>{const c=i.findIndex(v=>v.day===u.day.toLowerCase());c!==-1&&(i[c].timeslots=u.timeslots)}),T(i)}catch(s){console.error("Error parsing availability:",s),T([])}else T([])};a.useEffect(()=>{const e=s=>{K&&!s.target.closest("#contact-info-button")&&!s.target.closest(".contact-info-popover")&&xe(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[K]);const Pe=e=>{const s=e.target.value;X(s),Y&&clearTimeout(Y);const i=setTimeout(()=>{s.trim()?p(h,g,{},[`${y._project_id}_user.first_name,cs,${s}`]):p(h,g)},500);Ce(i)},ke={name:e=>{var s,i,u,c,v,E,N;return t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx("img",{src:((s=e.user)==null?void 0:s.photo)||"/default-avatar.png",alt:`${(i=e.user)==null?void 0:i.first_name} ${(u=e.user)==null?void 0:u.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),t.jsx("span",{className:"font-medium capitalize text-gray-900",children:!((c=e==null?void 0:e.user)!=null&&c.first_name)||!((v=e==null?void 0:e.user)!=null&&v.last_name)?"--":`${(E=e==null?void 0:e.user)==null?void 0:E.first_name} ${(N=e==null?void 0:e.user)==null?void 0:N.last_name}`})]})},status:e=>{var s;return t.jsx("span",{className:"text-gray-600",children:((s=e==null?void 0:e.user)==null?void 0:s.status)===1?"Active":"Inactive"})},role:e=>{var s,i;return t.jsx("span",{className:"capitalize text-gray-600",children:(s=e==null?void 0:e.user)!=null&&s.role?(i=e==null?void 0:e.user)==null?void 0:i.role:"--"})},email:e=>{var s;return t.jsx("span",{className:"text-gray-600",children:((s=e==null?void 0:e.user)==null?void 0:s.email)||"--"})},phone:e=>{var s;return t.jsx("span",{className:"text-gray-600",children:(s=e==null?void 0:e.user)!=null&&s.phone?qe(e.user.phone):"--"})},bank_details:e=>t.jsx("span",{className:"text-gray-600",children:(()=>{var s;try{if(e!=null&&e.account_details){const i=JSON.parse(e.account_details);return i&&Array.isArray(i)&&i.length>0&&((s=i[0])!=null&&s.account_number)?`•••• ${i[0].account_number.slice(-4)}`:"--"}return"--"}catch{return"--"}})()}),actions:e=>t.jsxs("div",{className:"flex items-center justify-end gap-3",children:[t.jsx(re,{to:`/${Z}/view-staff/${e.id}`,className:"rounded-full p-2 hover:bg-gray-100",onClick:s=>s.stopPropagation(),children:t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M6.88016 17.7625C7.97481 16.1634 9.77604 15.1243 11.9993 15.1243C14.2227 15.1243 16.0239 16.1634 17.1185 17.7625M6.88016 17.7625C8.24153 18.9726 10.0346 19.7077 11.9993 19.7077C13.9641 19.7077 15.7572 18.9726 17.1185 17.7625M6.88016 17.7625C5.29173 16.3505 4.29102 14.2918 4.29102 11.9993C4.29102 7.74215 7.74215 4.29102 11.9993 4.29102C16.2565 4.29102 19.7077 7.74215 19.7077 11.9993C19.7077 14.2918 18.707 16.3505 17.1185 17.7625M14.7077 10.3327C14.7077 11.8285 13.4951 13.041 11.9993 13.041C10.5036 13.041 9.29102 11.8285 9.29102 10.3327C9.29102 8.83691 10.5036 7.62435 11.9993 7.62435C13.4951 7.62435 14.7077 8.83691 14.7077 10.3327Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinejoin:"round"})})}),t.jsx("button",{onClick:s=>{s.stopPropagation(),Ne(e)},className:"rounded-full p-2 hover:bg-gray-100",children:t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M2.46209 9.95722L3.76445 17.3433C3.86035 17.8872 4.37901 18.2503 4.9229 18.1544L10.1162 17.2387M2.46209 9.95722L1.94114 7.0028C1.84524 6.45891 2.20841 5.94025 2.7523 5.84434L14.0776 3.84739C14.6215 3.75149 15.1401 4.11466 15.236 4.65855L15.757 7.61297L2.46209 9.95722ZM16.0002 11.7509V14.0009L18.0002 16.0009M22.2502 14.0009C22.2502 17.4527 19.452 20.2509 16.0002 20.2509C12.5485 20.2509 9.75025 17.4527 9.75025 14.0009C9.75025 10.5491 12.5485 7.75092 16.0002 7.75092C19.452 7.75092 22.2502 10.5491 22.2502 14.0009Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),t.jsx(re,{to:`/${Z}/view-staff/${e.id}`,className:"rounded-lg bg-blue-50 px-3 py-1 text-sm text-blue-600 hover:bg-blue-100",onClick:s=>s.stopPropagation(),children:"View Pay"}),t.jsx("button",{onClick:s=>{s.stopPropagation(),z(!0),ye({staff_id:e.id,user_id:e.user_id})},className:"rounded-full p-2 hover:bg-gray-100",children:t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M4.74071 16.928L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49738 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49738 4.97796Z",fill:"#868C98"})})})]})},Te=[{header:"Name",accessor:"name"},{header:"Status",accessor:"status"},{header:"Role",accessor:"role"},{header:"Email",accessor:"email"},{header:"Phone",accessor:"phone"},{header:"Bank details",accessor:"bank_details"},{header:"Actions",accessor:"actions"}];return t.jsxs("div",{className:"h-full",children:[t.jsxs("div",{className:"flex w-full flex-col justify-between gap-4 py-3 md:flex-row md:items-center",children:[t.jsx("div",{className:"flex flex-col gap-4 md:flex-row md:items-center md:gap-4",children:t.jsxs("div",{className:"relative flex max-w-md flex-1 items-center",children:[t.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:t.jsx(De,{className:"text-gray-500"})}),t.jsx("input",{type:"text",value:W,onChange:Pe,className:"block w-full min-w-[200px] rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search staff by name or email"}),W&&t.jsx("button",{onClick:()=>{X(""),p(h,g)},className:"absolute right-2 rounded-full p-1 hover:bg-gray-100",children:t.jsx(He,{className:"text-gray-500"})})]})}),t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsxs("button",{onClick:()=>ee(!0),className:"inline-flex items-center gap-2 rounded-lg border border-gray-200 px-4 py-2 text-sm font-semibold text-gray-700 hover:bg-gray-50",children:[t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M4.75038 16.8788V11.6684M4.75038 8.54214V3.12305M9.9992 16.6703V10.6259M9.99922 7.49978V3.33138M15.248 16.8785V13.3354M15.248 10.2095V3.12305M3.12109 11.46H6.45753M8.33253 7.70801H11.6659M13.5409 13.1271H16.8742",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),"Manage role access"]}),t.jsxs("button",{onClick:()=>Q(!0),className:"inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[t.jsx("span",{className:"",children:"+"}),"Add new"]})]}),t.jsx("div",{className:" ",children:t.jsx(Ue,{title:"Staff History",emptyMessage:"No staff history found",activityType:w.staff_management})})]}),U?t.jsx(Re,{}):t.jsx(Be,{columns:Te,data:P,loading:U,renderCustomCell:ke,rowClassName:"hover:bg-gray-50 bg-gray-100 px-4 py-3 cursor-pointer",emptyMessage:"No staff members found",loadingMessage:"Loading staff members...",onClick:e=>_e(`/${Z}/view-staff/${e.id}`)}),C>0&&t.jsx(Ze,{currentPage:h,pageCount:C,pageSize:g,canPreviousPage:n,canNextPage:f,updatePageSize:e=>{S(e),p(1,e)},previousPage:je,nextPage:Me,gotoPage:e=>p(e,g)}),F&&l&&t.jsx(Ie,{showTimesAvailableModal:F,setShowTimesAvailableModal:V,selectedTimes:H,setSelectedTimes:T,title:`${(ie=l==null?void 0:l.user)==null?void 0:ie.first_name} ${(ne=l==null?void 0:l.user)==null?void 0:ne.last_name}'s availability`,isSubmitting:me,onSave:async e=>{var s,i;try{q(!0);const c=(()=>H?H.filter(v=>v.timeslots.length>0):[])();y.setTable("staff"),await y.callRestAPI({id:l.id,availability:JSON.stringify(c)},"PUT"),await O(y,{user_id:te,activity_type:w.staff_management,action_type:B.UPDATE,data:{staff_id:l.id,availability:c},club_id:r==null?void 0:r.id,description:`Updated availability for ${(s=l==null?void 0:l.user)==null?void 0:s.first_name} ${(i=l==null?void 0:l.user)==null?void 0:i.last_name}`}),M(o,"Availability updated successfully",3e3,"success"),p(h,g)}catch(u){console.error("Error saving availability:",u),M(L,u.message,3e3,"error")}finally{q(!1),V(!1),J(null)}}}),t.jsx(Ve,{isOpen:ge,onClose:()=>z(!1),onDelete:()=>Le(pe),message:"Are you sure you want to delete this staff?",loading:he,title:"Delete Staff"}),ve&&t.jsx(Oe,{title:"Invite Staff",onClose:()=>Q(!1),link:`${window.location.origin}/staff/signup?club_id=${r==null?void 0:r.id}`,message:"Please use this following link to invite a Staff to join our website. Once the Staff has signed up,their details can be fetched and managed in our system."}),t.jsx(Je,{isOpen:be,onClose:()=>ee(!1),roleAccessData:Se,club:r})]})},ht=Ke;export{ht as L};
