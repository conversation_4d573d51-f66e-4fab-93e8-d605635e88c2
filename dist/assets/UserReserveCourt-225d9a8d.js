import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as i,f as xt,b as ee,L as Re}from"./vendor-851db8c1.js";import{M as yt,T as vt,G as wt,A as _t,u as jt,m as Nt,b as S,_ as St,az as bt,aA as Z,al as Ct,e as kt,v as I,aB as Tt,d as Bt,aq as Pt,E as Mt,D as Rt,H as Et}from"./index-ca7cbd3e.js";import{B as $t}from"./BackButton-11ba52b2.js";import{T as Lt}from"./TimeSlots-c9c10356.js";import{A as It}from"./AddPlayers-25b3db54.js";import{C as At}from"./Calendar-282b3fcf.js";import{S as Ft}from"./SportTypeSelection-295d9596.js";import{C as Ee}from"./ReservationSummary-8fd7b9ef.js";import{h as te}from"./moment-a9aaa855.js";import{S as Yt}from"./react-select-c8303602.js";import{A as Ot}from"./AccessRestricted-eb1272e0.js";import{g as Ht}from"./customThresholdUtils-f40b07d5.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./react-tooltip-7a26650a.js";import"./index.esm-09a3a6b8.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";let H=new yt,$e=new vt;const Fs=({})=>{var Ne,Se,be,Ce,ke,Te,Be,Pe,Me;const[c,se]=i.useState(null),[b,ne]=i.useState(null),[A,ie]=i.useState(new Date),[Le,Ie]=i.useState([]),[Ae,Fe]=i.useState([]),[F,Ye]=i.useState(0),[q,Oe]=i.useState(0),[qt,ae]=i.useState(!1),[T,P]=i.useState("main"),[g,V]=i.useState([]),[oe,He]=i.useState(!1),[K,z]=i.useState(1),[re,qe]=i.useState(!1),[le,Ve]=i.useState(3.5),[ce,De]=i.useState(3.5),[de,Ge]=i.useState(""),[Ue,Je]=i.useState([]),[x,me]=i.useState(null),[We,ue]=i.useState(!1),[l,ge]=i.useState(null),[p,pe]=i.useState(null),[w,Y]=i.useState([]),[E,fe]=i.useState([]),[h,D]=i.useState(null),[Ze,Ke]=i.useState(null),[ze,Qe]=i.useState(null),[Xe,he]=i.useState(!1),[et,tt]=i.useState(null),[xe,st]=i.useState(null),[nt,Q]=i.useState(!1),[M,C]=i.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),{state:Vt,dispatch:_}=i.useContext(wt);i.useContext(_t);const{club:s,pricing:ye,sports:R,loading:Dt,user_subscription:k,user_permissions:j,user_profile:v,club_membership:$,courts:G}=jt(),it=xt();console.log("club",s);const ve=localStorage.getItem("user"),a=ee.useMemo(()=>!(k!=null&&k.planId)||!($!=null&&$.length)?null:$.find(e=>e.plan_id===k.planId),[k,$]);console.log("userMembershipPlan",a),console.log("user_subscription",k),console.log("club_membership",$);const U=ee.useMemo(()=>{var d,u;if(((d=a==null?void 0:a.advance_booking_enabled)==null?void 0:d.court)===!1){const y=new Date;return y.setFullYear(y.getFullYear()+10),y}const e=((u=a==null?void 0:a.advance_booking_days)==null?void 0:u.court)||10,n=new Date,o=new Date;return o.setDate(n.getDate()+e),o},[a]),at=async()=>{try{const e=await $e.getList("user",{filter:["role,cs,user",`club_id,cs,${s==null?void 0:s.id}`]});Ie(e.list)}catch(e){console.error(e)}},ot=async()=>{try{const e=await $e.getList("user",{filter:[`guardian,eq,${ve}`,"role,cs,user"]});Je(e.list)}catch(e){console.error("Error fetching family members:",e)}},rt=async()=>{try{const e=await H.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");Fe(e.groups)}catch(e){console.error(e)}};i.useEffect(()=>{(async()=>(ue(!0),await rt(),await ot(),ue(!1)))()},[ve]),i.useEffect(()=>{s!=null&&s.id&&at()},[s==null?void 0:s.id]),i.useEffect(()=>{v&&!x&&me(v)},[v,x]),ee.useEffect(()=>{Nt({path:"/user/reserve-court",clubName:s==null?void 0:s.name,favicon:s==null?void 0:s.club_logo,description:"Reserve a court"})},[s==null?void 0:s.club_logo]),i.useEffect(()=>{},[T]);const lt=()=>{ie(new Date(A.setMonth(A.getMonth()-1)))},ct=()=>{ie(new Date(A.setMonth(A.getMonth()+1)))},r=R==null?void 0:R.find(e=>e.id===c),N=Ht(s==null?void 0:s.custom_request_threshold,c,l,p,4,R);i.useEffect(()=>{c&&(console.log("Selected Sport:",c),console.log("Selected Type:",l),console.log("Selected SubType:",p),console.log("Max Players Allowed:",N))},[c,l,p,N]),i.useEffect(()=>{g.length>N&&(console.log(`Clearing selected players: current ${g.length} exceeds new threshold ${N}`),V([]),z(1),S(_,`Player selection cleared. New maximum is ${N} players. Please select players again.`,4e3,"warning"))},[N]),i.useEffect(()=>{K>N-g.length&&z(Math.max(0,N-g.length))},[N,g.length]);const{start_time:we,end_time:_e,duration:B}=St(w),J=e=>{try{return e.court_settings?JSON.parse(e.court_settings):{min_booking_time:30,allow_reservation:!0,allow_lesson:!0,allow_clinic:!0,allow_buddy:!0}}catch(n){return console.warn(`Failed to parse court_settings for court ${e.id}:`,n),{min_booking_time:30,allow_reservation:!0,allow_lesson:!0,allow_clinic:!0,allow_buddy:!0}}},O=()=>{if(!h||!E.length)return 30;const e=E.find(o=>o.id===h);return e&&J(e).min_booking_time||30},dt=i.useCallback(({sport:e,type:n,subType:o})=>{e!==c?(se(e),ne(null),Y([]),D(null)):se(e),n!==l?(ge(n),e===c&&(Y([]),D(null))):ge(n),o!==p?(pe(o),e===c&&n===l&&(Y([]),D(null))):pe(o)},[c,l,p]);i.useEffect(()=>{if(G&&G.length>0){let e=[...G];c&&(e=e.filter(n=>n.sport_id&&n.sport_id.toString()===c.toString())),l&&(e=e.filter(n=>n.type===l)),p&&(e=e.filter(n=>n.sub_type===p)),e=e.filter(n=>J(n).allow_reservation!==!1),fe(e)}else fe([])},[G,c,l,p]);const mt=async()=>{var d,u,y;const e=(d=r==null?void 0:r.sport_types)==null?void 0:d.some(f=>f.type&&f.type.trim()!==""),n=(u=r==null?void 0:r.sport_types)==null?void 0:u.find(f=>f.type===l),o=(y=n==null?void 0:n.subtype)==null?void 0:y.some(f=>f&&f.trim()!=="");if(!c||e&&!l||e&&o&&!p||!b||!we||!_e){S(_,"Please select all required fields",3e3,"error");return}ae(!0);try{const f=await H.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",{amount:q},"POST");Ke(f.client_secret),Qe(f.payment_intent);const W=te(b).format("YYYY-MM-DD"),m={sport_id:c,type:l,sub_type:p,date:W,start_time:we,end_time:_e,duration:B,reservation_type:Pt.court,price:q,player_ids:g.map(ht=>ht.id),primary_player_id:(x==null?void 0:x.id)||(v==null?void 0:v.id),buddy_details:null,payment_status:0,payment_intent:f.payment_intent,service_fee:Z(s==null?void 0:s.fee_settings,F),club_fee:s==null?void 0:s.club_fee,players_needed:K,min_ntrp:le,max_ntrp:ce,note:de};(s==null?void 0:s.allow_user_court_selection)===1&&h&&(m.court_id=h);const X=await H.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",m,"POST");return await Mt(H,{user_id:localStorage.getItem("user"),activity_type:Rt.court_reservation,action_type:Et.CREATE,data:m,club_id:s==null?void 0:s.id,description:`${v==null?void 0:v.first_name} ${v==null?void 0:v.last_name} created a court reservation`}),X.error||S(_,"Reservation created successfully",3e3,"success"),st(X.reservation_id),X.booking_id}catch(f){console.error(f),S(_,f.message||"Error creating reservation",3e3,"error")}finally{ae(!1)}},je=e=>{V(n=>n.some(d=>d.id===e.id)?n.filter(d=>d.id!==e.id):[...n,e])},ut=e=>{const n=e.value||e;(n==null?void 0:n.id)!==(x==null?void 0:x.id)&&(me(n),V(o=>{const d=o.filter(y=>y.id!==(x==null?void 0:x.id));if(d.some(y=>y.id===n.id)){const y=d.filter(f=>f.id!==n.id);return[n,...y]}else return[n,...d]}))},gt=async()=>{Q(!0);try{const e=await H.callRawAPI(`/v3/api/custom/courtmatchup/user/reservations/availability/10?sport_id=${c}&type=${l}`,{},"GET");console.log(e),Q(!1),P("players")}catch(e){console.error(e),Q(!1)}};i.useEffect(()=>{if(g!=null&&g.length&&c&&l&&p&&(w!=null&&w.length)&&B){const e=bt({pricing:ye,sportId:c,type:l,subType:p,duration:B,selectedTime:w[0]}),n=Z(s==null?void 0:s.fee_settings,e);Ye(e),Oe(e+n)}},[g,c,l,p,w,ye,s==null?void 0:s.fee_settings]);const pt=async()=>{var d,u,y,f,W;if(!(k!=null&&k.planId)){C({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to reserve courts",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(j!=null&&j.allowCourt)){C({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${j==null?void 0:j.planName}) does not include court reservations. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(b>U&&((d=a==null?void 0:a.advance_booking_enabled)!=null&&d.court)){const m=`Your membership plan only allows booking ${((u=a==null?void 0:a.advance_booking_days)==null?void 0:u.court)||10} days in advance. Please select a valid date.`;C({isOpen:!0,title:"Date Selection Error",message:m,type:"warning"}),P("main");return}const e=(y=r==null?void 0:r.sport_types)==null?void 0:y.some(m=>m.type&&m.type.trim()!==""),n=(f=r==null?void 0:r.sport_types)==null?void 0:f.find(m=>m.type===l),o=(W=n==null?void 0:n.subtype)==null?void 0:W.some(m=>m&&m.trim()!=="");if(!c||e&&!l||e&&o&&!p||!b||!w.length){C({isOpen:!0,title:"Incomplete Details",message:"Please complete all required Reservation detail",type:"warning"}),P("main");return}if((s==null?void 0:s.allow_user_court_selection)===1&&!h){C({isOpen:!0,title:"Court Selection Required",message:"Please select a court for your reservation",type:"warning"});return}if(h&&w.length>0){const m=O();if(B<m){C({isOpen:!0,title:"Minimum Booking Time Not Met",message:`The selected court requires a minimum booking time of ${m} minutes. Your current selection is ${B} minutes. Please select a longer time slot.`,type:"warning"}),P("main");return}}if(!g.length){C({isOpen:!0,title:"Players Required",message:"Please select at least one player",type:"warning"});return}try{he(!0);const m=await mt();if(!m)throw new Error("Failed to create reservation");tt(m),P("payment")}catch(m){console.error("Reservation error:",m),C({isOpen:!0,title:"Reservation Error",message:m.message||"Error creating reservation",type:"error"})}finally{he(!1)}},ft=e=>{const n=te(e.from,"h:mm A"),d=te(e.until,"h:mm A").diff(n,"minutes");if(h){const u=O();if(d<u){S(_,`Minimum booking time for this court is ${u} minutes. Please select a longer time slot.`,4e3,"warning");return}}Y([{from:e.from,until:e.until}])},L=s!=null&&s.court_description?JSON.parse(s==null?void 0:s.court_description):{reservation_description:"",payment_description:""};return j&&!j.allowCourt?t.jsx(Ot,{message:`Your current plan (${j==null?void 0:j.planName}) does not include court reservations. Please upgrade your plan to access this feature.`}):t.jsxs("div",{className:"",children:[t.jsx(Ct,{isOpen:M.isOpen,onClose:()=>C({...M,isOpen:!1}),title:M.title,message:M.message,actionButtonText:M.actionButtonText,actionButtonLink:M.actionButtonLink,type:M.type}),We&&t.jsx(kt,{}),t.jsxs("div",{className:"flex items-center justify-center bg-white p-4",children:[T==="main"&&t.jsx("div",{className:" ",children:"Step 1 • Select date and time"}),T==="players"&&t.jsx("div",{className:" ",children:"Step 2 • Reservation detail"}),T==="payment"&&t.jsx("div",{className:" ",children:"Step 3 • Payment"})]}),t.jsxs("div",{className:"p-4",children:[t.jsx($t,{onBack:()=>{T==="main"?it(-1):P(T==="payment"?"players":"main")}}),T==="main"?t.jsx("div",{className:"p-4",children:t.jsx("div",{className:"space-y-6",children:t.jsx("div",{className:"mx-auto max-w-7xl p-4",children:t.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[t.jsx(Ft,{sports:R,userPermissions:j,initialSport:c,initialType:l,initialSubType:p,onSelectionChange:dt}),c&&(!((Ne=r==null?void 0:r.sport_types)!=null&&Ne.length)||l!==null&&(p!==null||!((Ce=(be=(Se=r==null?void 0:r.sport_types)==null?void 0:Se.find(e=>e.type===l))==null?void 0:be.subtype)!=null&&Ce.length)))?t.jsxs(t.Fragment,{children:[t.jsx("div",{children:t.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[(ke=a==null?void 0:a.advance_booking_enabled)!=null&&ke.court?t.jsxs("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["You can reserve a court up to"," ",(Te=a==null?void 0:a.advance_booking_days)==null?void 0:Te.court," ",((Be=a==null?void 0:a.advance_booking_days)==null?void 0:Be.court)===1?"day":"days"," ","in advance."]}):t.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:"You can reserve a court for any future date."}),t.jsx(At,{currentMonth:A,selectedDate:b,onDateSelect:e=>{var n,o;if(e>U){const d=(n=a==null?void 0:a.advance_booking_enabled)!=null&&n.court?`Your membership plan only allows booking ${((o=a==null?void 0:a.advance_booking_days)==null?void 0:o.court)||10} days in advance`:"";if(d){S(_,d,3e3,"warning");return}}ne(e)},onPreviousMonth:lt,onNextMonth:ct,daysOff:s!=null&&s.days_off?JSON.parse(s.days_off):[],allowPastDates:!1,minDate:new Date,maxDate:U,disabledDateMessage:(Pe=a==null?void 0:a.advance_booking_enabled)!=null&&Pe.court?`Your membership plan only allows booking ${((Me=a==null?void 0:a.advance_booking_days)==null?void 0:Me.court)||10} days in advance`:"You can book for any future date"})]})}),b&&t.jsx(t.Fragment,{children:t.jsx(Lt,{selectedDate:b,timeRange:w,onTimeClick:ft,isLoading:nt,onNext:()=>{var e,n;if(!w.length){S(_,"Please select a time slot",3e3,"error");return}if(h&&B>0){const o=O();if(B<o){S(_,`Minimum booking time for this court is ${o} minutes. Please select a longer time slot.`,4e3,"warning");return}}if(b>U){const o=(e=a==null?void 0:a.advance_booking_enabled)!=null&&e.court?`Your membership plan only allows booking ${((n=a==null?void 0:a.advance_booking_days)==null?void 0:n.court)||10} days in advance`:"";if(o){S(_,o,3e3,"warning");return}}gt()},nextButtonText:"Next: Players",startHour:0,endHour:24,interval:30,className:"h-fit",isTimeSlotAvailable:()=>!0,clubTimes:s!=null&&s.times?JSON.parse(s.times):[],minBookingTime:h?O():30,enforceMinBookingTime:!!h})})]}):t.jsx("div",{className:"flex h-full items-center justify-center rounded-lg bg-white p-8 shadow-5 md:col-span-2",children:t.jsxs("div",{className:"text-center text-gray-500",children:[t.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),t.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Please select a sport"}),t.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose a sport, type, and sub-type to view available time slots"})]})})]})})})}):T==="payment"?t.jsxs("div",{className:"mx-auto max-w-6xl",children:[t.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:t.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),t.jsx("span",{className:"!text-sm",children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),t.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[t.jsx("div",{className:"space-y-6",children:t.jsx(Ee,{selectedSport:c,sports:R,selectedType:l,selectedSubType:p,selectedDate:b,selectedTimes:w,selectedCourt:h?E.find(e=>e.id===h):null})}),t.jsx("div",{className:"space-y-6",children:t.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[t.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-gray-500",children:"Club fee"}),t.jsx("span",{children:I(F)})]}),t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-gray-500",children:"Service fee"}),t.jsx("span",{children:I(Z(s==null?void 0:s.fee_settings,F))})]}),t.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[t.jsx("span",{className:"font-medium",children:"Total"}),t.jsx("span",{className:"font-medium",children:I(q)})]}),t.jsxs("div",{children:[t.jsx(Tt,{user:v,bookingId:et,reservationId:xe,clientSecret:Ze,paymentIntent:ze,navigateRoute:`/user/payment-success/${xe}?type=court`}),t.jsx("div",{className:"mt-4",children:t.jsx("div",{className:"text-sm text-gray-500",children:L==null?void 0:L.payment_description})})]}),t.jsx("div",{className:"space-y-4 text-sm text-gray-500",children:t.jsxs("p",{children:['By clicking "Pay now" you agree to our'," ",t.jsx(Re,{to:"/terms-and-conditions",target:"_blank",className:"font-medium underline",children:"Terms and Conditions"})," ","and"," ",t.jsx(Re,{to:"/privacy-policy",target:"_blank",className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise."]})})]})]})})]})]}):t.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[t.jsxs("div",{className:"space-y-4",children:[" ",t.jsx(Ee,{selectedSport:c,sports:R,selectedType:l,selectedSubType:p,selectedDate:b,selectedTimes:w,selectedCourt:h?E.find(e=>e.id===h):null})]}),t.jsxs("div",{className:"space-y-4",children:[(s==null?void 0:s.allow_user_court_selection)===1&&t.jsxs("div",{className:"mb-2 h-fit rounded-lg bg-white p-4 shadow-5",children:[t.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-900",children:"Select Court"}),t.jsx(Yt,{className:"w-full text-sm",options:E.map(e=>{const n=J(e);return{value:e.id,label:`${e.name} (Min: ${n.min_booking_time}min)`}}),onChange:e=>{if(D(e?e.value:null),e&&w.length>0){const n=O();B<n&&(Y([]),S(_,`Selected court requires minimum ${n} minutes. Please select a new time slot.`,4e3,"info"))}},value:h?(()=>{const e=E.find(n=>n.id===h);if(e){const n=J(e);return{value:h,label:`${e.name} (Min: ${n.min_booking_time}min)`}}return null})():null,isClearable:!0,placeholder:"Select a court",noOptionsMessage:()=>{var n,o,d;if(!c)return"Please select a sport first";if((n=r==null?void 0:r.sport_types)!=null&&n.some(u=>u.type&&u.type.trim()!=="")&&!l)return"Please select a type";const e=(o=r==null?void 0:r.sport_types)==null?void 0:o.find(u=>u.type===l);return(d=e==null?void 0:e.subtype)!=null&&d.some(u=>u&&u.trim()!=="")&&!p?"Please select a sub-type":"No courts available for the selected criteria"}}),t.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Select your preferred court for this reservation. Each court shows its minimum booking time requirement."})]}),t.jsx(It,{players:Le,groups:Ae,selectedPlayers:g,familyMembers:Ue,currentUser:x,onCurrentUserChange:ut,onPlayerToggle:e=>{if(g.some(o=>o.id===e.id)){if(e.id===(x==null?void 0:x.id)){S(_,"You cannot remove the primary player from the reservation",3e3,"warning");return}je(e);return}if(g.length>=N){const o=(r==null?void 0:r.name)||"this sport";S(_,`Maximum ${N} players allowed for ${o} (including yourself)`,3e3,"warning");return}je(e)},isFindBuddyEnabled:oe,setSelectedPlayers:V,onFindBuddyToggle:()=>{He(!oe),qe(!re)},playersNeeded:K,onPlayersNeededChange:z,maximumPlayers:N,userProfile:v,showPlayersNeeded:re,onNtrpMinChange:Ve,onNtrpMaxChange:De,onShortBioChange:Ge,initialNtrpMin:le,initialNtrpMax:ce,initialShortBio:de})]}),t.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[t.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:t.jsx("h2",{className:"text-base font-medium",children:"Reservation detail"})}),t.jsx("div",{className:"p-4",children:t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{children:[t.jsxs("h3",{className:"text-sm text-gray-500",children:["PLAYERS (",g==null?void 0:g.length,")"]}),t.jsx("div",{className:"mt-1",children:g.length>0&&g.map(e=>t.jsxs("div",{className:"text-sm",children:[e.first_name," ",e.last_name]},e.id))})]}),t.jsxs("div",{children:[t.jsx("h3",{className:"text-sm text-gray-500",children:"FEES"}),t.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[t.jsx("span",{children:"Club Fee"}),t.jsx("span",{children:I(F)})]}),t.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[t.jsx("span",{children:"Service Fee"}),t.jsx("span",{children:I(Z(s==null?void 0:s.fee_settings,F))})]})]}),t.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[t.jsx("span",{children:"Total"}),t.jsx("span",{className:"font-medium",children:I(q)})]}),t.jsx("div",{className:"rounded-lg bg-[#F17B2C] p-3 text-sm text-white",children:t.jsxs("div",{className:"flex items-start gap-2",children:[t.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 7.99995 2.33325C8.09995 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z",fill:"white"})}),t.jsx("span",{children:"After reserving, you will have 15 minutes to make the payment."})]})}),t.jsx(Bt,{loading:Xe,onClick:pt,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:t.jsxs("div",{className:"flex flex-col items-center",children:[t.jsx("span",{children:"Reserve Now"}),t.jsx("span",{className:"text-sm opacity-80",children:"and continue to payment"})]})}),t.jsx("div",{className:"text-center text-sm text-gray-500",children:L==null?void 0:L.reservation_description}),t.jsx("div",{className:"space-y-2 text-center text-sm text-gray-500",children:t.jsx("p",{children:"(You will not be charged yet)"})})]})})]})]})]})]})};export{Fs as default};
