import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{f as q,r as c,b as i}from"./vendor-851db8c1.js";import{M as B,G as Y,Z as z,aF as Q,Y as u,a2 as $,v as w,b as T,e as Ns,A as bs,R as Bs,aa as Ys,T as Gs,i as Us,W as Hs,X as hs,t as Vs}from"./index-ca7cbd3e.js";import{c as qs,a as K}from"./yup-54691517.js";import{u as zs}from"./react-hook-form-687afde5.js";import{o as Zs}from"./yup-2824f222.js";import{P as Ks}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import Js from"./Skeleton-1e8bf077.js";import{L as Qs}from"./LoadingOverlay-87926629.js";import{R as W,T as X,P as v,F as ss,G as _s,L as Cs}from"./ReservationStatus-0d38d99f.js";import{h as V}from"./moment-a9aaa855.js";import{C as es}from"./ReserveCourtModal-bd4f1835.js";import{S as Ws}from"./react-select-c8303602.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@hookform/resolvers-67648cca.js";import"./react-loading-skeleton-3d87d1f5.js";const fs=new B;function Xs({reservation:e,club:L,clubSports:d,players:t,onReservationCanceled:m}){var S;const p=q(),{dispatch:y}=c.useContext(Y),[g,N]=c.useState(!1),[o,j]=c.useState(!1),f=z(e==null?void 0:e.reservation_updated_at),M=Q(e,d),C=async()=>{j(!0);try{fs.setTable("reservation"),await fs.callRestAPI({id:e==null?void 0:e.reservation_id},"DELETE"),T(y,"Reservation cancelled successfully",3e3,"success"),N(!1),m&&m()}catch(a){console.error("Error cancelling reservation:",a),T(y,a.message||"Error cancelling reservation",3e3,"error")}finally{j(!1)}};return s.jsxs("div",{children:[s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsx("div",{children:(e==null?void 0:e.booking_status)===u.PENDING&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(W,{}),s.jsx(X,{timeLeft:f})]})||(e==null?void 0:e.booking_status)===u.SUCCESS&&s.jsx(v,{})||(e==null?void 0:e.booking_status)===u.FAIL&&s.jsx(ss,{})})})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(S=d==null?void 0:d.find(a=>a.id===(e==null?void 0:e.sport_id)))==null?void 0:S.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),s.jsx("p",{className:"mt-1 font-medium",children:V(e==null?void 0:e.booking_date).format("MMM D, YYYY")})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[$(e==null?void 0:e.start_time)," -"," ",$(e==null?void 0:e.end_time)]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),s.jsx("div",{className:"flex flex-col gap-2",children:t.map(a=>s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:a.photo||"/default-avatar.png",alt:a.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:a.first_name||a.last_name?`${a.first_name} ${a.last_name}`:"Player"})]},a.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:w(e==null?void 0:e.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:w(e==null?void 0:e.service_fee)})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:w((e==null?void 0:e.club_fee)+(e==null?void 0:e.service_fee))})]})]}),(e==null?void 0:e.booking_status)==u.PENDING&&s.jsx("div",{className:"mt-3 flex items-center justify-between",children:s.jsx("button",{onClick:()=>{p(`/user/reservation-payment/${e==null?void 0:e.reservation_id}?type=${e==null?void 0:e.booking_type}`)},disabled:f==="0min",className:`w-full rounded-lg border border-gray-300 px-2 py-2 text-sm text-white ${f==="0min"?"cursor-not-allowed bg-gray-400":"bg-primaryBlue"}`,children:f==="0min"?"Time Expired":"Pay now"})}),(e==null?void 0:e.booking_status)==u.SUCCESS&&s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{p(`/user/payment-receipt/${e==null?void 0:e.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),M&&(e==null?void 0:e.booking_status)===u.SUCCESS&&s.jsx("div",{className:"mt-3",children:s.jsx("button",{onClick:()=>N(!0),className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]}),s.jsx(es,{isOpen:g,onClose:()=>N(!1),onDelete:C,loading:o})]})}const gs=new B;function vs({reservation:e,club:L,clubSports:d,players:t,coach:m,onReservationCanceled:p}){var a;const y=q(),{dispatch:g}=c.useContext(Y),[N,o]=c.useState(!1),[j,f]=c.useState(!1),M=z(e==null?void 0:e.reservation_updated_at),C=Q(e,d),S=async()=>{f(!0);try{gs.setTable("reservation"),await gs.callRestAPI({id:e==null?void 0:e.reservation_id},"DELETE"),T(g,"Reservation cancelled successfully",3e3,"success"),o(!1),p&&p()}catch(n){console.error("Error cancelling reservation:",n),T(g,n.message||"Error cancelling reservation",3e3,"error")}finally{f(!1)}};return s.jsxs("div",{children:[s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{children:(e==null?void 0:e.booking_status)===u.PENDING&&s.jsx(W,{})||(e==null?void 0:e.booking_status)===u.SUCCESS&&s.jsx(v,{})||(e==null?void 0:e.booking_status)===u.FAIL&&s.jsx(ss,{})}),s.jsx("div",{children:s.jsx(X,{timeLeft:M})})]})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(a=d==null?void 0:d.find(n=>n.id===(e==null?void 0:e.sport_id)))==null?void 0:a.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),s.jsx("p",{className:"mt-1 font-medium",children:V(e==null?void 0:e.booking_date).format("MMM D, YYYY")})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[$(e==null?void 0:e.start_time)," -"," ",$(e==null?void 0:e.end_time)]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"COACH"})}),s.jsx("div",{className:"flex flex-col gap-2",children:m&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:m.photo||"/default-avatar.png",alt:m.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:m.first_name||m.last_name?`${m.first_name} ${m.last_name}`:"Coach"})]},m.user_id)})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),s.jsx("div",{className:"flex flex-col gap-2",children:t.map(n=>s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:n.photo||"/default-avatar.png",alt:n.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:n.first_name||n.last_name?`${n.first_name} ${n.last_name}`:"Player"})]},n.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:w(L==null?void 0:L.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:"$12.50"})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:w((e==null?void 0:e.club_fee)+12.5)})]})]}),s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{y(`/user/payment-receipt/${e==null?void 0:e.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),C&&(e==null?void 0:e.booking_status)===u.SUCCESS&&s.jsx("div",{className:"mt-3",children:s.jsx("button",{onClick:()=>o(!0),className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]}),s.jsx(es,{isOpen:N,onClose:()=>o(!1),onDelete:S,loading:j})]})}const js=new B;function se({reservation:e,club:L,clubSports:d,players:t,onReservationCanceled:m}){var S;const p=q(),{dispatch:y}=c.useContext(Y),[g,N]=c.useState(!1),[o,j]=c.useState(!1),f=z(e==null?void 0:e.reservation_updated_at),M=Q(e,d),C=async()=>{j(!0);try{js.setTable("reservation"),await js.callRestAPI({id:e==null?void 0:e.reservation_id},"DELETE"),T(y,"Reservation cancelled successfully",3e3,"success"),N(!1),m&&m()}catch(a){console.error("Error cancelling reservation:",a),T(y,a.message||"Error cancelling reservation",3e3,"error")}finally{j(!1)}};return s.jsxs("div",{children:[s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsx("div",{children:(e==null?void 0:e.booking_status)===u.PENDING&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(W,{}),s.jsx(X,{timeLeft:f})]})||(e==null?void 0:e.booking_status)===u.SUCCESS&&s.jsx(v,{})||(e==null?void 0:e.booking_status)===u.FAIL&&s.jsx(ss,{})})})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(S=d==null?void 0:d.find(a=>a.id===(e==null?void 0:e.sport_id)))==null?void 0:S.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),s.jsx("p",{className:"mt-1 font-medium",children:V(e==null?void 0:e.booking_date).format("MMM D, YYYY")})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[$(e==null?void 0:e.start_time)," -"," ",$(e==null?void 0:e.end_time)]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),s.jsx("div",{className:"flex flex-col gap-2",children:t==null?void 0:t.map(a=>s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:a.photo||"/default-avatar.png",alt:a.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:a.first_name||a.last_name?`${a.first_name} ${a.last_name}`:"Player"})]},a.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:w(e==null?void 0:e.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:w(e==null?void 0:e.service_fee)})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:w((e==null?void 0:e.club_fee)+(e==null?void 0:e.service_fee))})]})]}),s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{p(`/user/payment-receipt/${e==null?void 0:e.reservation_id}?type=clinic`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),M&&(e==null?void 0:e.booking_status)===u.SUCCESS&&s.jsx("div",{className:"mt-3",children:s.jsx("button",{onClick:()=>N(!0),className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]}),s.jsx(es,{isOpen:g,onClose:()=>N(!1),onDelete:C,loading:o})]})}const ts=new B;function ee({reservation:e,club:L,clubSports:d,players:t,onReservationCanceled:m}){var I;const p=q(),y=z(e==null?void 0:e.reservation_updated_at),{dispatch:g}=c.useContext(Y),[N,o]=c.useState(!1),[j,f]=c.useState(!1),[M,C]=c.useState(!1),S=Q(e,d),a=async x=>{if(!x){T(g,"Email is required",5e3,"error");return}try{o(!0);const D=await ts.callRawAPI(`/v3/api/custom/courtmatchup/user/buddy/${e==null?void 0:e.buddy_id}/send-mail?email=${x}`,{},"GET");console.log(D),T(g,"Email reminder sent",5e3,"success")}catch(D){console.log(D),T(g,D==null?void 0:D.message,5e3,"error")}finally{o(!1)}},n=async()=>{C(!0);try{ts.setTable("reservation"),await ts.callRestAPI({id:e==null?void 0:e.reservation_id},"DELETE"),T(g,"Reservation cancelled successfully",3e3,"success"),f(!1),m&&m()}catch(x){console.error("Error cancelling reservation:",x),T(g,x.message||"Error cancelling reservation",3e3,"error")}finally{C(!1)}};return s.jsxs("div",{children:[N&&s.jsx(Ns,{}),s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsxs("div",{children:[(e==null?void 0:e.num_needed)==(e==null?void 0:e.num_players)&&s.jsx(_s,{title:"Group full"}),(e==null?void 0:e.num_needed)!=(e==null?void 0:e.num_players)&&s.jsx(Cs,{numberOfBuddies:e==null?void 0:e.num_needed})]})})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"REQUEST MADE"})}),V(e==null?void 0:e.reservation_created_at).format("MMM D, YYYY")]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DTE & TIME"})}),V(e==null?void 0:e.booking_date).format("MMM D, YYYY")," •"," ",$(e==null?void 0:e.start_time)," -"," ",$(e==null?void 0:e.end_time)]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(I=d==null?void 0:d.find(x=>x.id===(e==null?void 0:e.sport_id)))==null?void 0:I.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"NOTES"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[e==null?void 0:e.ntrp," ",(e==null?void 0:e.max_ntrp)&&`- ${e==null?void 0:e.max_ntrp}`]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"LOOKING FOR PLAYERS"})}),s.jsx("p",{className:"mt-1 font-medium",children:`${e==null?void 0:e.num_needed}/${e==null?void 0:e.num_players}`})]}),s.jsxs("div",{className:"py-3",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"}),s.jsx("button",{className:"rounded-lg border border-gray-300 bg-white px-2 py-1 text-sm text-gray-700",children:"Email all"})]}),s.jsx("div",{className:"mt-4 flex flex-col gap-4",children:t.map(x=>s.jsxs("div",{className:"flex items-center justify-between rounded-lg bg-gray-100 p-2",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("img",{src:x.photo||"/default-avatar.png",alt:x.first_name,className:"h-10 w-10 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("p",{className:"font-medium capitalize",children:x.first_name||x.last_name?`${x.first_name} ${x.last_name}`:"Player"}),s.jsx("p",{className:"text-sm text-gray-500",children:x.email})]})]}),s.jsxs("div",{className:"flex flex-col items-end gap-3",children:[s.jsx("button",{onClick:()=>a(x.email),className:"rounded-lg bg-white px-2 py-1 text-sm text-gray-700",children:"Send email"}),s.jsxs("p",{className:"text-sm text-gray-700",children:["NTRP: ",x.ntrp]})]})]},x.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:w(e==null?void 0:e.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:w(e==null?void 0:e.service_fee)})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:w((e==null?void 0:e.club_fee)+(e==null?void 0:e.service_fee))})]})]}),(e==null?void 0:e.booking_status)==u.PENDING&&s.jsx("div",{className:"mt-3 flex items-center justify-between",children:s.jsx("button",{onClick:()=>{p(`/user/reservation-payment/${e==null?void 0:e.reservation_id}?type=${e==null?void 0:e.booking_type}`)},disabled:y==="0min",className:`w-full rounded-lg border border-gray-300 px-2 py-2 text-sm text-white ${y==="0min"?"cursor-not-allowed bg-gray-400":"bg-primaryBlue"}`,children:y==="0min"?"Time Expired":"Pay now"})}),(e==null?void 0:e.booking_status)==u.SUCCESS&&s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{p(`/user/payment-receipt/${e==null?void 0:e.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),S&&(e==null?void 0:e.booking_status)===u.SUCCESS&&s.jsx("div",{className:"mt-3",children:s.jsx("button",{onClick:()=>f(!0),className:"w-full rounded-lg border border-red-300 bg-red-50 px-2 py-2 text-sm text-red-600 hover:bg-red-100",children:"Cancel Reservation"})}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]}),s.jsx(es,{isOpen:j,onClose:()=>f(!1),onDelete:n,loading:M})]})}let ys=new B;function le({isOpen:e,onClose:L,clubSports:d,reservation:t,club:m,onReservationCanceled:p}){const{dispatch:y}=c.useContext(Y),{dispatch:g}=c.useContext(bs),[N,o]=c.useState(!1),[j,f]=c.useState([]),[M,C]=c.useState(null);async function S(){try{const n=await Ys(y,g,"user",JSON.parse(t==null?void 0:t.player_ids),"user|user_id");f(n.list)}catch(n){console.error(n)}}async function a(){try{ys.setTable("user");const n=await ys.callRestAPI({id:t==null?void 0:t.coach_id},"GET");C(n.model)}catch(n){console.error(n)}}return c.useEffect(()=>{(async()=>(o(!0),await S(),await a(),o(!1)))()},[t]),t?N?s.jsx(Ns,{}):(console.log({reservation:t,sports:d,players:j,coach:M}),s.jsxs(Bs,{isOpen:e,onClose:L,title:t.booking_type==="Court"?"Court details":t.booking_type==="Coach"?"Coach details":t.booking_type==="Clinic"?"Clinic details":"Details",showFooter:!1,className:"!p-0",children:[t.booking_type==="Court"&&s.jsx(Xs,{reservation:t,club:m,clubSports:d,players:j,onReservationCanceled:p}),t.booking_type==="Find Buddy"&&s.jsx(ee,{reservation:t,club:m,clubSports:d,players:j,onReservationCanceled:p}),t.booking_type==="Coach"&&s.jsx(vs,{reservation:t,club:m,clubSports:d,players:j,coach:M,onReservationCanceled:p}),t.booking_type==="Clinic"&&s.jsx(se,{reservation:t,club:m,clubSports:d,players:j,onReservationCanceled:p})]})):null}let J=new B,as=new Gs;const os=[{header:"Date & Time",accessor:"date",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Booking Type",accessor:"booking_type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Players",accessor:"players",isSorted:!1,isSortedDesc:!1,mappingExist:!1},{header:"Price",accessor:"price",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"reservation_status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{1:"Active",0:"Inactive"}}],Ke=()=>{const{dispatch:e,state:L}=i.useContext(Y),{dispatch:d}=i.useContext(bs),[t,m]=i.useState([]),[p,y]=i.useState(10),[g,N]=i.useState(0);i.useState(0);const[o,j]=i.useState(0),[f,M]=i.useState(!1),[C,S]=i.useState(!1),[a,n]=i.useState(!1);i.useState(!1),i.useState([]),i.useState([]),i.useState("eq");const[I,x]=i.useState(!0),[D,te]=i.useState(!1),[Ss,ae]=i.useState(!1);i.useState(),q();const cs=i.useRef(null),[ce,rs]=i.useState(!1),[ds,is]=i.useState(null);i.useState([]),c.useState(!1);const[ks,Es]=c.useState([]);c.useState([]),c.useState([]);const[G,ws]=c.useState("upcoming");c.useState(!1),c.useState(!1);const[Ms,Ts]=c.useState(null),[U,Ps]=c.useState([]),[P,ms]=c.useState(null),[ns,Ls]=c.useState(null),Rs=[{id:"upcoming",label:"Upcoming",icon:()=>s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.05153 8.29769L3.13684 14.4527C3.21675 14.906 3.64897 15.2086 4.10222 15.1287L8.42993 14.3656M2.05153 8.29769L1.61741 5.83567C1.53749 5.38242 1.84013 4.95021 2.29338 4.87029L11.7311 3.20616C12.1844 3.12624 12.6166 3.42888 12.6965 3.88213L13.1306 6.34414L2.05153 8.29769ZM13.3333 9.79243V11.6674L15 13.3341M18.5417 11.6674C18.5417 14.5439 16.2098 16.8758 13.3333 16.8758C10.4569 16.8758 8.125 14.5439 8.125 11.6674C8.125 8.79095 10.4569 6.4591 13.3333 6.4591C16.2098 6.4591 18.5417 8.79095 18.5417 11.6674Z",stroke:"black","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})},{id:"past",label:"Past",icon:()=>s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[s.jsx("rect",{width:"20",height:"20",fill:"white"}),s.jsx("path",{d:"M12.5 7.91602L8.75001 12.4993L7.08334 10.8327M17.7083 9.99935C17.7083 14.2565 14.2572 17.7077 10 17.7077C5.74281 17.7077 2.29167 14.2565 2.29167 9.99935C2.29167 5.74215 5.74281 2.29102 10 2.29102C14.2572 2.29102 17.7083 5.74215 17.7083 9.99935Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})},{id:"cancelled",label:"Cancelled",icon:()=>s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M12.5 7.49935L7.5 12.4993M12.5 12.4993L7.5 7.49935M17.7083 9.99935C17.7083 14.2565 14.2572 17.7077 10 17.7077C5.7428 17.7077 2.29166 14.2565 2.29166 9.99935C2.29166 5.74215 5.7428 2.29102 10 2.29102C14.2572 2.29102 17.7083 5.74215 17.7083 9.99935Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round"})})}],Ds=qs({id:K(),email:K(),role:K(),status:K()});zs({resolver:Zs(Ds)});function $s(){A()}function As(){A()}async function A(l,r,H={},_=[]){x(!(Ss||D));try{let h;if(P&&P.value!=="all"&&P.value!=="me"?h=await J.callRawAPI(`/v3/api/custom/courtmatchup/user/family-reservations/${P.value.id}`,{},"GET"):h=await J.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",{},"GET"),h){x(!1);const F=new Date;F.setHours(0,0,0,0);let b=h.list||h.reservations||[];if(P&&P.value==="all"&&U.length>0)try{const k=U.map(E=>J.callRawAPI(`/v3/api/custom/courtmatchup/user/family-reservations/${E.id}`,{},"GET"));(await Promise.all(k)).forEach(E=>{if(E&&(E.list||E.reservations)){const O=E.list||E.reservations||[];b=[...b,...O]}}),b=b.filter((E,O,Z)=>O===Z.findIndex(ls=>ls.id===E.id))}catch(k){console.error("Error fetching family reservations:",k)}G==="upcoming"?b=b.filter(k=>{const R=new Date(k.booking_date);return R.setHours(0,0,0,0),R>=F}):G==="past"?b=b.filter(k=>{const R=new Date(k.booking_date);return R.setHours(0,0,0,0),R<F}):G==="cancelled"&&(b=b.filter(k=>k.booking_status===2)),m(b)}}catch(h){x(!1),console.log("ERROR",h),Vs(d,h.message)}}const Is=async()=>{try{const l=localStorage.getItem("user"),r=await as.getOne("user",l,{}),H=await J.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${r.model.club_id}`,{},"GET");Es(H.sports),Ts(H.model)}catch(l){console.error(l)}},Fs=async()=>{try{const l=localStorage.getItem("user"),r=await as.getList("user",{filter:[`guardian,eq,${l}`,"role,cs,user"]});Ps(r.list)}catch(l){console.error("Error fetching family members:",l)}},Os=async()=>{try{const l=localStorage.getItem("user"),r=await as.getOne("user",l,{});Ls(r.model)}catch(l){console.error("Error fetching user profile:",l)}};i.useEffect(()=>{e({type:"SETPATH",payload:{path:"my-reservations"}});const r=setTimeout(async()=>{await A(1,p,{}),await Is(),await Fs(),await Os()},700);return()=>{clearTimeout(r)}},[]),i.useEffect(()=>{A(1,p,{})},[G]),i.useEffect(()=>{P!==null&&A(1,p,{})},[P]),i.useEffect(()=>{P===null&&ms({value:"all",label:"All Reservations"})},[U,ns]);const xs=l=>{cs.current&&!cs.current.contains(l.target)&&n(!1)};i.useEffect(()=>(document.addEventListener("mousedown",xs),()=>{document.removeEventListener("mousedown",xs)}),[]);const ps=l=>{is(l),rs(!0)};console.log("reservation data",t);const us=[{value:"all",label:"All Reservations"},{value:"me",label:"My Reservations"},...U.map(l=>({value:l,label:`${l.first_name} ${l.last_name} (${l.family_role||"Family Member"})`}))];return s.jsxs("div",{children:[s.jsxs("div",{className:"bg-white px-4 pt-4",children:[s.jsx("h1",{className:"mb-6 text-2xl font-semibold",children:"My Reservations"}),(U.length>0||ns)&&s.jsxs("div",{className:"mb-6 max-w-sm",children:[s.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-900",children:"Filter by family member"}),s.jsx(Ws,{className:"w-full text-sm",options:us,onChange:ms,value:P,placeholder:"Select family member",isSearchable:!1,defaultValue:us[0]})]}),s.jsx("div",{className:"mb-0 flex max-w-fit  text-sm",children:Rs.map(l=>s.jsxs("button",{onClick:()=>ws(l.id),className:`flex items-center gap-2 bg-transparent px-3 py-3 ${G===l.id?"border-b-2 border-primaryBlue":""}`,children:[l.icon(),s.jsx("span",{className:"",children:l.label})]},l.id))})]}),s.jsxs("div",{className:"h-screen px-8",children:[I&&s.jsx(Qs,{}),I?s.jsx(Js,{}):s.jsxs("div",{className:"overflow-x-auto",children:[s.jsxs("table",{className:"w-full min-w-[1024px] ",children:[s.jsx("thead",{children:s.jsx("tr",{children:os.map((l,r)=>s.jsx("th",{scope:"col",className:"px-6 py-4 text-left text-sm font-medium text-gray-500",children:l.header},r))})}),s.jsx("tbody",{className:"divide-y-8 divide-gray-50",children:t.map((l,r)=>{const H=z(l==null?void 0:l.reservation_updated_at);return s.jsx("tr",{onClick:()=>ps(l),className:"hover:bg-gray-40 cursor-pointer rounded-lg bg-white px-4 py-3 text-gray-500",children:os.map((_,h)=>{var F,b,k,R,E,O,Z;return _.accessor==""?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.jsx("div",{className:"flex items-center gap-3",children:s.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",onClick:()=>ps(l),children:s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})})},h):_.accessor==="reservation_status"?s.jsxs("td",{className:"flex gap-2 whitespace-nowrap px-6 py-5 text-sm",children:[(l==null?void 0:l.booking_type)=="Find Buddy"&&s.jsxs(s.Fragment,{children:[(l==null?void 0:l.num_needed)==(l==null?void 0:l.num_players)&&s.jsx(_s,{title:"Group full"}),(l==null?void 0:l.num_needed)!=(l==null?void 0:l.num_players)&&s.jsx(Cs,{numberOfBuddies:l==null?void 0:l.num_needed})]}),(l==null?void 0:l.booking_status)===u.PENDING&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(W,{}),s.jsx(X,{timeLeft:H})]})||(l==null?void 0:l.booking_status)===u.SUCCESS&&s.jsx(v,{})||(l==null?void 0:l.booking_status)===u.FAIL&&s.jsx(ss,{})]},h):_.mappingExist?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:_.mappings[l[_.accessor]]},h):_.accessor==="type"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:(F=Us.find(ls=>ls.value===(l==null?void 0:l.type)))==null?void 0:F.label},h):_.accessor==="date"?s.jsxs("td",{className:"whitespace-nowrap rounded-l-3xl px-6 py-4",children:[Hs((l==null?void 0:l.booking_date)||"")||"--"," "," | "," ",hs((l==null?void 0:l.start_time)||"")||"--"," "," - "," ",hs((l==null?void 0:l.end_time)||"")||"--"]},h):_.accessor==="booking_type"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:(l==null?void 0:l.booking_type)||"--"},h):_.accessor==="players"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:(b=l==null?void 0:l.booking)!=null&&b.player_ids?`${JSON.parse((k=l==null?void 0:l.booking)==null?void 0:k.player_ids).length} players`:"0 players"},h):_.accessor==="price"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:w((l==null?void 0:l.price)||0)},h):_.accessor==="user"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:!((R=l==null?void 0:l.user)!=null&&R.first_name)||!((E=l==null?void 0:l.user)!=null&&E.last_name)?"--":`${(O=l==null?void 0:l.user)==null?void 0:O.first_name} ${(Z=l==null?void 0:l.user)==null?void 0:Z.last_name}`},h):s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:l[_.accessor]},h)})},r)})})]}),!I&&t.length===0&&s.jsx("div",{className:"w-full px-6 py-4 text-center",children:s.jsx("p",{className:"text-gray-500",children:"No data available"})})]}),s.jsx(Ks,{currentPage:o,pageCount:g,pageSize:p,canPreviousPage:f,canNextPage:C,updatePageSize:l=>{y(l),A()},previousPage:$s,nextPage:As,gotoPage:l=>A()}),s.jsx(le,{isOpen:!!ds,onClose:()=>is(null),reservation:ds,clubSports:ks,club:Ms})]})," "]})};export{Ke as default};
