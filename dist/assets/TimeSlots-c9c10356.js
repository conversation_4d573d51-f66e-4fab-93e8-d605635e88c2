import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{r as y}from"./vendor-851db8c1.js";import{d as G}from"./index-ca7cbd3e.js";import{M as ie}from"./react-tooltip-7a26650a.js";import{f as re}from"./date-fns-07266b7d.js";const oe=({selectedDate:T,timeRange:ae,onTimeClick:M,onNext:k,nextButtonText:J="Next",startHour:C=8,endHour:E=24,interval:L=30,className:K="",multipleSlots:I=!1,timeSlots:le=[],onTimeSlotsChange:p,individualSelection:me=!1,isTimeSlotAvailable:de,clubTimes:R=[],isLoading:Q,coachAvailability:S=[],height:V="h-fit",minBookingTime:W=30,enforceMinBookingTime:X=!1})=>{var U;const[f,H]=y.useState([]),[o,h]=y.useState([]),[Y,j]=y.useState(350),D=y.useRef(null);y.useEffect(()=>{const e=()=>{const t=window.innerHeight;t<600?j(200):t<800?j(300):j(350)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);const b=y.useCallback(()=>{const e=[];for(let t=C;t<=E;t++)for(let n=0;n<60;n+=L){const i=t===24?0:t,r=`${i.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}`,a=i>=12?"PM":"AM",c=`${i===0?12:i>12?i-12:i}:${n.toString().padStart(2,"0")} ${a}`;e.push({time24:r,time12:c})}return e},[C,E,L]),A=e=>{if(!R||R.length===0)return!0;const[t,n]=e.split(":"),i=parseInt(t)*60+parseInt(n);return R.some(r=>{const[a,l]=r.from.split(":"),[c,u]=r.until.split(":"),v=parseInt(a)*60+parseInt(l),d=parseInt(c)*60+parseInt(u);return i>=v&&i<=d})},_=e=>f.some(t=>{const n=b(),i=n.findIndex(l=>l.time12===t.from),r=n.findIndex(l=>l.time12===t.until),a=n.findIndex(l=>l.time24===e);return a>=i&&a<=r}),z=e=>{if(!T||!S||S.length===0)return!0;const t=T.toLocaleDateString("en-US",{weekday:"long"}).toLowerCase(),n=S.find(r=>r.day===t);if(!n)return!1;const i=`${e}:00`;return n.timeslots.includes(i)},O=e=>{var d,F,P,Z;if(!z(e.time24)||!A(e.time24)||I&&_(e.time24))return;const t=b(),n=t.findIndex(m=>m.time24===e.time24);if(o.includes(e.time24)){if(o.length>=2){const m=[...o].sort();if(e.time24===m[0]||e.time24===m[m.length-1]){const N=o.filter(g=>g!==e.time24);h(N);const x=[...N].sort(),B=(d=t.find(g=>g.time24===x[0]))==null?void 0:d.time12;let q;const ne=t.findIndex(g=>g.time24===x[x.length-1]),$=t[ne+1];q=($==null?void 0:$.time12)||((F=t.find(g=>g.time24===x[x.length-1]))==null?void 0:F.time12),M({from:B,until:q})}}else o.length===1&&(h([]),M({from:"",until:""}));return}if(o.length===0)h([e.time24]);else{const m=[...o].sort(),N=m[m.length-1],x=t.findIndex(B=>B.time24===N);Math.abs(n-x)===1?h([...o,e.time24]):h([e.time24])}const r=[...o.length===0?[e.time24]:[...o,e.time24]].sort(),a=(P=t.find(m=>m.time24===r[0]))==null?void 0:P.time12;let l;const c=t.findIndex(m=>m.time24===r[r.length-1]),u=t[c+1];l=(u==null?void 0:u.time12)||((Z=t.find(m=>m.time24===r[r.length-1]))==null?void 0:Z.time12),M({from:a,until:l})},ee=()=>{var u,v;if(o.length===0)return;const e=b(),t=[...o].sort(),n=(u=e.find(d=>d.time24===t[0]))==null?void 0:u.time12;let i;const r=e.findIndex(d=>d.time24===t[t.length-1]),a=e[r+1];i=(a==null?void 0:a.time12)||((v=e.find(d=>d.time24===t[t.length-1]))==null?void 0:v.time12);const l={from:n,until:i},c=[...f,l];H(c),h([]),p==null||p(c)},te=e=>{const t=f.filter((n,i)=>i!==e);H(t),p==null||p(t)},se=e=>o.includes(e),w=b();return s.jsxs("div",{className:`rounded-lg bg-white p-4 shadow-5 ${K} ${V}`,children:[T&&s.jsx("p",{className:"text-center font-medium",children:re(T,"EEEE, MMMM d, yyyy")}),X&&s.jsxs("div",{className:"mb-3 mt-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["Minimum booking time: ",W," minutes"]}),s.jsx("div",{ref:D,className:"scrollbar-hide mb-5 mt-2 flex h-full flex-col gap-2 overflow-y-auto",style:{maxHeight:`${Y}px`},children:w.map((e,t)=>{const n=A(e.time24),i=z(e.time24),r=I&&_(e.time24),a=i&&n&&!r,l=n?i?"":"Coach not available":"Club Closed";return s.jsxs("button",{onClick:()=>O(e),disabled:!a,"data-tooltip-id":`time-${t}`,"data-tooltip-content":l,type:"button",className:`
                rounded-lg border-[1.5px] px-4 py-2 text-sm font-medium transition-colors
                ${a?se(e.time24)?"border-[1.5px] border-primaryBlue bg-primaryBlue/10 text-primaryBlue":"border-gray-200 text-gray-500 hover:bg-gray-50":"cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400"}
              `,children:[e.time12,!a&&s.jsx(ie,{id:`time-${t}`,place:"top",className:"z-50 !bg-gray-900 !px-2 !py-1 !text-xs !text-white"})]},e.time24)})}),o.length>0&&s.jsxs("div",{className:"space-y-2 border-t border-gray-200 pt-4",children:[s.jsxs("div",{className:"flex items-center justify-center gap-2 text-sm",children:[s.jsx("span",{className:"font-medium",children:"From: "}),s.jsx("span",{className:"text-primaryBlue",children:(U=w.find(e=>e.time24===o.sort()[0]))==null?void 0:U.time12}),s.jsx("span",{className:"font-medium",children:"Until: "}),s.jsx("span",{className:"text-primaryBlue",children:(()=>{var i;const e=[...o].sort(),t=w.findIndex(r=>r.time24===e[e.length-1]),n=w[t+1];return(n==null?void 0:n.time12)||((i=w.find(r=>r.time24===e[e.length-1]))==null?void 0:i.time12)})()})]}),I&&s.jsx(G,{className:"mt-2 w-full rounded-lg border border-primaryBlue bg-primaryBlue/10 px-4 py-2 text-primaryBlue hover:bg-primaryBlue/20",onClick:ee,disabled:o.length===0,children:"Add Time Range"})]}),I&&f.length>0&&s.jsxs("div",{className:"mt-4 space-y-2 border-t border-gray-200 pt-4",children:[s.jsx("p",{className:"text-center font-medium",children:"Selected Time Ranges"}),s.jsx("div",{className:"flex flex-col justify-center gap-2 ",children:f.map((e,t)=>s.jsxs("div",{className:"grid grid-cols-[auto_auto_auto_auto_auto] items-center gap-2 rounded-lg px-3 py-1 text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"From"}),s.jsx("span",{children:e.from}),s.jsx("span",{children:"-"}),s.jsx("span",{className:"text-gray-500",children:"Until"}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("span",{children:e.until}),s.jsx("button",{onClick:()=>te(t),className:"text-primaryBlue hover:text-primaryBlue/80",children:s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M15 9L9 15M15 15L9 9M21.25 12C21.25 17.1086 17.1086 21.25 12 21.25C6.89137 21.25 2.75 17.1086 2.75 12C2.75 6.89137 6.89137 2.75 12 2.75C17.1086 2.75 21.25 6.89137 21.25 12Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round"})})})]})]},t))})]}),k&&s.jsx("div",{className:"sticky bottom-0 bg-white pt-2",children:s.jsx(G,{className:"mt-2 w-full rounded-lg bg-primaryBlue px-4 py-2 text-white disabled:opacity-50",onClick:k,disabled:I?f.length===0:o.length===0,loading:Q,children:J})})]})},he=oe;export{he as T};
